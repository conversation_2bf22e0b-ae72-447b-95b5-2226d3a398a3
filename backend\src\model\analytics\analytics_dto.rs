use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, NaiveDate};
use uuid::Uuid;
use std::collections::HashMap;
/// 分析看板主数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalyticsDashboard {
    pub overview: AnalyticsOverview,
    pub real_time_metrics: RealTimeMetrics,
    pub behavior_analysis: BehaviorAnalysisData,
    pub online_user_analysis: OnlineUserAnalysisData,
    pub trends: TrendsData,
}

/// 分析总览数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalyticsOverview {
    pub total_users: i64,
    pub active_users_today: i64,
    pub total_sessions_today: i64,
    pub avg_session_duration_minutes: f64,
    pub total_page_views_today: i64,
    pub total_actions_today: i64,
    pub current_online_users: i64,
    pub peak_concurrent_today: i64,
    // 增长百分比字段
    pub user_growth_percentage: f64,
    pub active_users_growth_percentage: f64,
    pub page_views_growth_percentage: f64,
}

/// 实时指标数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RealTimeMetrics {
    pub current_online: i64,
    pub active_last_5min: i64,
    pub active_last_15min: i64,
    pub active_last_hour: i64,
    pub actions_last_hour: i64,
    pub avg_response_time_ms: f64,
    pub error_rate_percent: f64,
    pub new_sessions_last_hour: i64,
}

/// 用户行为分析数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BehaviorAnalysisData {
    pub most_active_modules: Vec<ModuleUsageStats>,
    pub popular_pages: Vec<PageUsageStats>,
    pub hourly_activity: Vec<HourlyStats>,
    pub device_usage: Vec<DeviceUsageStats>,
    pub user_journey: Vec<UserJourneyStep>,
}

/// 在线用户分析数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OnlineUserAnalysisData {
    pub distribution_by_status: Vec<StatusDistribution>,
    pub distribution_by_module: Vec<ModuleDistribution>,
    pub distribution_by_device: Vec<DeviceDistribution>,
    pub distribution_by_tenant: Vec<TenantDistribution>,
    pub session_duration_ranges: Vec<SessionDurationRange>,
}

/// 趋势数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendsData {
    pub daily_active_users: Vec<DailyTrend>,
    pub daily_sessions: Vec<DailyTrend>,
    pub daily_page_views: Vec<DailyTrend>,
    pub hourly_concurrent_users: Vec<HourlyTrend>,
    pub module_usage_trends: Vec<ModuleTrend>,
}

/// 模块使用统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModuleUsageStats {
    pub module: String,
    pub total_users: i64,
    pub total_actions: i64,
    pub avg_duration_minutes: f64,
    pub growth_rate: f64,
    pub popularity_rank: i32,
}

/// 页面使用统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageUsageStats {
    pub page_path: String,
    pub page_title: Option<String>,
    pub total_views: i64,
    pub unique_visitors: i64,
    pub avg_time_on_page_seconds: f64,
    pub bounce_rate: f64,
    pub exit_rate: f64,
}

/// 小时级统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HourlyStats {
    pub hour: i32,
    pub total_users: i64,
    pub total_actions: i64,
    pub avg_response_time_ms: f64,
    pub error_count: i64,
    pub peak_concurrent: i64,
}

/// 设备使用统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceUsageStats {
    pub device_type: String,
    pub user_count: i64,
    pub session_count: i64,
    pub avg_session_duration_minutes: f64,
    pub percentage: f64,
}

/// 用户行为路径
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserJourneyStep {
    pub step_order: i32,
    pub page_path: String,
    pub module: String,
    pub user_count: i64,
    pub avg_time_spent_seconds: f64,
    pub drop_off_rate: f64,
    pub next_most_common_page: Option<String>,
}

/// 状态分布
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StatusDistribution {
    pub status: String,
    pub count: i64,
    pub percentage: f64,
}

/// 模块分布
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModuleDistribution {
    pub module: String,
    pub active_users: i64,
    pub percentage: f64,
    pub avg_session_duration_minutes: f64,
}

/// 设备分布
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceDistribution {
    pub device_type: String,
    pub count: i64,
    pub percentage: f64,
}

/// 租户分布
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TenantDistribution {
    pub tenant_id: String,
    pub tenant_name: String,
    pub user_count: i64,
    pub percentage: f64,
}

/// 会话时长范围分布
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionDurationRange {
    pub range_label: String, // "0-5分钟", "5-15分钟", "15-30分钟", "30分钟以上"
    pub min_minutes: i32,
    pub max_minutes: Option<i32>, // None表示无上限
    pub user_count: i64,
    pub percentage: f64,
}

/// 日趋势数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DailyTrend {
    pub date: NaiveDate,
    pub value: i64,
    pub change_from_previous: f64,
    pub change_percentage: f64,
}

/// 小时趋势数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HourlyTrend {
    pub hour: i32,
    pub value: i64,
    pub avg_value: f64,
}

/// 模块趋势数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModuleTrend {
    pub module: String,
    pub daily_data: Vec<DailyTrend>,
    pub total_growth_rate: f64,
}

/// 用户行为漏斗分析
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BehaviorFunnel {
    pub funnel_name: String,
    pub steps: Vec<FunnelStep>,
    pub overall_conversion_rate: f64,
    pub total_users_entered: i64,
    pub total_users_completed: i64,
}

/// 漏斗步骤
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunnelStep {
    pub step_name: String,
    pub step_order: i32,
    pub user_count: i64,
    pub conversion_rate: f64,
    pub drop_off_count: i64,
    pub avg_time_to_next_step_seconds: Option<f64>,
}

/// 用户留存分析
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserRetentionAnalysis {
    pub period_type: String, // "daily", "weekly", "monthly"
    pub cohorts: Vec<RetentionCohort>,
    pub overall_retention_rates: HashMap<i32, f64>, // day/week/month -> retention rate
}

/// 留存队列
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetentionCohort {
    pub cohort_start: NaiveDate,
    pub cohort_size: i64,
    pub retention_rates: HashMap<i32, f64>, // period -> retention rate
}

/// 用户行为热图数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserBehaviorHeatmap {
    pub date_range: DateRange,
    pub heatmap_data: Vec<HeatmapCell>,
    pub max_intensity: f64,
    pub min_intensity: f64,
}

/// 热图单元格
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeatmapCell {
    pub hour: i32,
    pub day_of_week: i32, // 0-6 (周日到周六)
    pub intensity: f64,   // 活动强度
    pub user_count: i64,
    pub action_count: i64,
}

/// 日期范围
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DateRange {
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
}

/// API查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalyticsQueryParams {
    pub tenant_id: Option<Uuid>,
    pub start_date: Option<NaiveDate>,
    pub end_date: Option<NaiveDate>,
    pub module: Option<String>,
    pub user_id: Option<Uuid>,
    pub device_type: Option<String>,
    pub include_real_time: Option<bool>,
    pub include_trends: Option<bool>,
    pub trend_days: Option<i32>,
} 