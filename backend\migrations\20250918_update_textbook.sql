ALTER TABLE public.textbooks ADD COLUMN IF NOT EXISTS subject_code varchar(20);
ALTER TABLE public.textbooks ADD COLUMN IF NOT EXISTS grade_level_code varchar(20);
ALTER TABLE public.textbooks ADD COLUMN IF NOT EXISTS distributor varchar(40);
ALTER TABLE public.textbooks ADD COLUMN IF NOT EXISTS printing_version varchar(20);
ALTER TABLE public.textbooks ADD COLUMN IF NOT EXISTS authors text[];
ALTER TABLE public.textbooks ADD COLUMN IF NOT EXISTS summary text;
ALTER TABLE public.textbooks ADD COLUMN IF NOT EXISTS edition varchar(20);
ALTER TABLE public.textbooks ADD COLUMN IF NOT EXISTS resource_version varchar(20);
ALTER TABLE public.textbooks ADD COLUMN IF NOT EXISTS origin_id bigint;
CREATE UNIQUE INDEX IF NOT EXISTS idx_textbooks_origin_id ON public.textbooks (origin_id);
ALTER TABLE public.textbooks ADD COLUMN IF NOT EXISTS is_deleted bool NOT NULL DEFAULT false;

ALTER TABLE public.questions ADD COLUMN IF NOT EXISTS origin_id bigint;
CREATE UNIQUE INDEX IF NOT EXISTS idx_questions_origin_id ON public.questions (origin_id);
ALTER TABLE public.questions ADD COLUMN IF NOT EXISTS is_deleted bool NOT NULL DEFAULT false;
ALTER TABLE public.question_answers ADD COLUMN IF NOT EXISTS origin_id bigint;
ALTER TABLE public.question_answers ALTER COLUMN answer_area_id TYPE text;
CREATE UNIQUE INDEX IF NOT EXISTS idx_question_answers_origin_id ON public.question_answers (origin_id);
ALTER TABLE public.question_answers ADD COLUMN IF NOT EXISTS is_deleted bool NOT NULL DEFAULT false;
ALTER TABLE public.papers ADD COLUMN IF NOT EXISTS resource_version varchar(20);
ALTER TABLE public.papers ADD COLUMN IF NOT EXISTS origin_id bigint;
CREATE UNIQUE INDEX IF NOT EXISTS idx_papers_origin_id ON public.papers (origin_id);
ALTER TABLE public.papers ADD COLUMN IF NOT EXISTS is_deleted bool NOT NULL DEFAULT false;
ALTER TABLE public.sections ADD COLUMN IF NOT EXISTS origin_id bigint;
CREATE UNIQUE INDEX IF NOT EXISTS idx_sections_origin_id ON public.sections (origin_id);
ALTER TABLE public.sections ADD COLUMN IF NOT EXISTS is_deleted bool NOT NULL DEFAULT false;
ALTER TABLE public.catalogs ADD COLUMN IF NOT EXISTS origin_id bigint;
CREATE UNIQUE INDEX IF NOT EXISTS idx_catalogs_origin_id ON public.catalogs (origin_id);
ALTER TABLE public.catalogs ADD COLUMN IF NOT EXISTS is_deleted bool NOT NULL DEFAULT false;