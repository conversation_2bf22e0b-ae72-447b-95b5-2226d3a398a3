@use '../global.module.scss' as base;

.button-base {
  @extend .unset;
  @extend .font-default;
  @extend .border-default;
  border-radius: base.$border-radius-large;
  cursor: pointer;
  color: #303133;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-default {
  @extend .button-base;
  @extend .hover-default;
  line-height: 32px;
  height: 32px;
  box-sizing: border-box;
}

.button-primary {
  @extend .button-base;

  background-color: #171717;
  color: #ffffff;

  &:hover {
    background-color: #313131;
  }

  &:active {
    background-color: #4d4d4d;
  }
}
.button-warning {
  @extend .button-base;

  background-color: #ff922c;
  color: #ffffff;

  &:hover {
    background-color: #ff7b00;
  }

  &:active {
    background-color: #ff7b00;
  }
}
