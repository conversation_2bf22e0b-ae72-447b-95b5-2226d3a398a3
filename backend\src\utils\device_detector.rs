use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub device_type: String,
    pub browser: String,
    pub os: String,
    pub user_agent: Option<String>,
}

impl DeviceInfo {
    /// 从User-Agent字符串中解析设备信息
    pub fn from_user_agent(user_agent: &str) -> Self {
        let device_type = detect_device_type(user_agent);
        let browser = detect_browser(user_agent);
        let os = detect_os(user_agent);

        Self {
            device_type,
            browser,
            os,
            user_agent: Some(user_agent.to_string()),
        }
    }

    /// 创建未知设备信息
    pub fn unknown() -> Self {
        Self {
            device_type: "unknown".to_string(),
            browser: "unknown".to_string(),
            os: "unknown".to_string(),
            user_agent: None,
        }
    }
}

/// 检测设备类型
fn detect_device_type(user_agent: &str) -> String {
    let ua = user_agent.to_lowercase();

    if ua.contains("mobile") || ua.contains("android") || ua.contains("iphone") {
        "mobile".to_string()
    } else if ua.contains("tablet") || ua.contains("ipad") {
        "tablet".to_string()
    } else {
        "desktop".to_string()
    }
}

/// 检测浏览器类型
fn detect_browser(user_agent: &str) -> String {
    let ua = user_agent.to_lowercase();

    if ua.contains("edge") || ua.contains("edg/") {
        "edge".to_string()
    } else if ua.contains("chrome") && !ua.contains("chromium") {
        "chrome".to_string()
    } else if ua.contains("firefox") {
        "firefox".to_string()
    } else if ua.contains("safari") && !ua.contains("chrome") {
        "safari".to_string()
    } else if ua.contains("opera") || ua.contains("opr/") {
        "opera".to_string()
    } else if ua.contains("chromium") {
        "chromium".to_string()
    } else if ua.contains("msie") || ua.contains("trident") {
        "ie".to_string()
    } else {
        "unknown".to_string()
    }
}

/// 检测操作系统
fn detect_os(user_agent: &str) -> String {
    let ua = user_agent.to_lowercase();

    if ua.contains("windows nt 10.0") {
        "windows_10".to_string()
    } else if ua.contains("windows nt 6.3") {
        "windows_8_1".to_string()
    } else if ua.contains("windows nt 6.2") {
        "windows_8".to_string()
    } else if ua.contains("windows nt 6.1") {
        "windows_7".to_string()
    } else if ua.contains("windows") {
        "windows".to_string()
    } else if ua.contains("macintosh") || ua.contains("mac os x") {
        "macos".to_string()
    } else if ua.contains("linux") {
        "linux".to_string()
    } else if ua.contains("android") {
        "android".to_string()
    } else if ua.contains("iphone") || ua.contains("ipad") {
        "ios".to_string()
    } else if ua.contains("cros") {
        "chrome_os".to_string()
    } else {
        "unknown".to_string()
    }
} 