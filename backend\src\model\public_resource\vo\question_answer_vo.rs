use std::collections::HashMap;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::model::public_resource::question_answer::QuestionAnswer;
use crate::utils::date_time;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct AnswerJson {
    pub id: String,
    pub question_id: String,
    pub answer_area_id: String,
    pub content: String,
    pub explanation: Option<String>,
    #[serde(deserialize_with = "date_time::deserialize")]
    pub update_at: DateTime<Utc>,
    #[serde(default)]
    pub is_deleted: bool,
}

impl QuestionAnswer {
    pub fn from(value: AnswerJson, question_map: &HashMap<i64, Uuid>) -> Self {
        let origin_id = value.id.parse().ok();
        let question_id = question_map.get(&value.question_id.parse::<i64>().unwrap_or_default()).cloned().unwrap_or_default();
        Self {
            id: Default::default(),
            question_id,
            answer_area_id: value.answer_area_id.clone(),
            content: value.content,
            explanation: value.explanation,
            updated_at: Some(value.update_at),
            origin_id,
            is_deleted: value.is_deleted,
        }
    }
}