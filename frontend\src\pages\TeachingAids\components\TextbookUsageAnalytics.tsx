import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import {
  Eye,
  Download,
  BookOpen,
  Users,
  Activity,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import { textbookAuthorizationApi } from '@/services/textbookAuthorizationApi.ts';
import type { TextbookTenantUsage } from '@/types/textbookAuthorization.ts';

// 定义统计数据类型
interface OverallStats {
  totalTextbooks: number;
  totalTenants: number;
  totalAccess: number;
  totalDownloads: number;
}

// 格式化日期的辅助函数
const formatDate = (dateString: string | null): string => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

interface TextbookUsageAnalyticsProps {
  className?: string;
}

const TextbookUsageAnalytics: React.FC<TextbookUsageAnalyticsProps> = ({ className }) => {
  const [usageData, setUsageData] = useState<TextbookTenantUsage[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTextbook, setSelectedTextbook] = useState<string>('');
  const [selectedTenant, setSelectedTenant] = useState<string>('');

  // 加载使用统计数据
  const loadUsageData = async () => {
    setLoading(true);
    try {
      const data = await textbookAuthorizationApi.getUsageStatistics(
        selectedTextbook || undefined,
        selectedTenant || undefined
      );
      setUsageData(data || []);
    } catch (error) {
      console.error('Failed to load usage data:', error);
      toast.error('无法加载使用统计数据，请重试');
      setUsageData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsageData();
  }, [selectedTextbook, selectedTenant]);

  // 计算总体统计
  const overallStats: OverallStats = (usageData || []).reduce(
    (acc, item) => ({
      totalTextbooks: new Set([...Array.from(acc.totalTextbooks), item.textbook_id]).size,
      totalTenants: new Set([...Array.from(acc.totalTenants), item.tenant_id]).size,
      totalAccess: acc.totalAccess + item.access_count,
      totalDownloads: acc.totalDownloads + item.download_count,
    }),
    {
      totalTextbooks: new Set<string>(),
      totalTenants: new Set<string>(),
      totalAccess: 0,
      totalDownloads: 0
    }
  );

  // 按教辅书聚合数据
  const textbookStats = (usageData || []).reduce((acc, item) => {
    const existing = acc.find(x => x.textbook_id === item.textbook_id);
    if (existing) {
      existing.access_count += item.access_count;
      existing.download_count += item.download_count;
      existing.tenant_count += 1;
    } else {
      acc.push({
        textbook_id: item.textbook_id,
        access_count: item.access_count,
        download_count: item.download_count,
        tenant_count: 1,
      });
    }
    return acc;
  }, [] as Array<{
    textbook_id: string;
    access_count: number;
    download_count: number;
    tenant_count: number;
  }>);

  // 按租户聚合数据
  const tenantStats = (usageData || []).reduce((acc, item) => {
    const existing = acc.find(x => x.tenant_id === item.tenant_id);
    if (existing) {
      existing.access_count += item.access_count;
      existing.download_count += item.download_count;
      existing.textbook_count += 1;
    } else {
      acc.push({
        tenant_id: item.tenant_id,
        access_count: item.access_count,
        download_count: item.download_count,
        textbook_count: 1,
      });
    }
    return acc;
  }, [] as Array<{
    tenant_id: string;
    access_count: number;
    download_count: number;
    textbook_count: number;
  }>);

  // 活跃度数据
  const activityData = (usageData || [])
    .filter(item => item.last_accessed_at)
    .sort((a, b) => new Date(b.last_accessed_at!).getTime() - new Date(a.last_accessed_at!).getTime())
    .slice(0, 10);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">使用统计分析</h2>
          <p className="text-muted-foreground">教辅书使用情况统计和分析</p>
        </div>
        <Button onClick={loadUsageData} disabled={loading} variant="outline" size="sm">
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? '加载中...' : '刷新数据'}
        </Button>
      </div>

      {/* 筛选器 */}
      <Card>
        <CardHeader>
          <CardTitle>筛选条件</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="textbook-filter">按教辅书筛选</Label>
              <Input
                id="textbook-filter"
                placeholder="输入教辅书ID（可选）"
                value={selectedTextbook}
                onChange={(e) => setSelectedTextbook(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="tenant-filter">按租户筛选</Label>
              <Input
                id="tenant-filter"
                placeholder="输入租户ID（可选）"
                value={selectedTenant}
                onChange={(e) => setSelectedTenant(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 总体统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">教辅书总数</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.totalTextbooks}</div>
            <p className="text-xs text-muted-foreground">有使用记录的教辅书</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">租户总数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.totalTenants}</div>
            <p className="text-xs text-muted-foreground">使用教辅书的租户</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总访问次数</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.totalAccess}</div>
            <p className="text-xs text-muted-foreground">累计访问次数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总下载次数</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.totalDownloads}</div>
            <p className="text-xs text-muted-foreground">累计下载次数</p>
          </CardContent>
        </Card>
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 教辅书使用排行 */}
        <Card>
          <CardHeader>
            <CardTitle>教辅书使用排行</CardTitle>
            <CardDescription>按访问次数排序的热门教辅书</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={textbookStats.slice(0, 10)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="textbook_id"
                  tickFormatter={(value) => `${value.slice(0, 8)}...`}
                />
                <YAxis />
                <Tooltip
                  labelFormatter={(value) => `教辅书: ${value}`}
                  formatter={(value, name) => [value, name === 'access_count' ? '访问次数' : '下载次数']}
                />
                <Bar dataKey="access_count" fill="#8884d8" name="访问次数" />
                <Bar dataKey="download_count" fill="#82ca9d" name="下载次数" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* 租户使用分布 */}
        <Card>
          <CardHeader>
            <CardTitle>租户使用分布</CardTitle>
            <CardDescription>各租户的使用情况对比</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={tenantStats.slice(0, 5)}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ tenant_id, access_count }) =>
                    `${tenant_id.slice(0, 8)}: ${access_count}`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="access_count"
                >
                  {tenantStats.slice(0, 5).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value, name) => [value, '访问次数']} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* 最近活跃记录 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            最近活跃记录
          </CardTitle>
          <CardDescription>最近访问的教辅书记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>教辅书ID</TableHead>
                <TableHead>租户ID</TableHead>
                <TableHead>访问次数</TableHead>
                <TableHead>下载次数</TableHead>
                <TableHead>最后访问时间</TableHead>
                <TableHead>最后下载时间</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {activityData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    暂无活跃记录
                  </TableCell>
                </TableRow>
              ) : (
                activityData.map((item) => (
                  <TableRow key={`${item.textbook_id}-${item.tenant_id}`}>
                    <TableCell className="font-mono text-sm">
                      {item.textbook_id.slice(0, 8)}...
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {item.tenant_id.slice(0, 8)}...
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">{item.access_count}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">{item.download_count}</Badge>
                    </TableCell>
                    <TableCell>
                      {formatDate(item.last_accessed_at)}
                    </TableCell>
                    <TableCell>
                      {formatDate(item.last_downloaded_at)}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 详细数据表格 */}
      <Card>
        <CardHeader>
          <CardTitle>详细使用数据</CardTitle>
          <CardDescription>所有教辅书使用记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>教辅书ID</TableHead>
                <TableHead>租户ID</TableHead>
                <TableHead>访问次数</TableHead>
                <TableHead>下载次数</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>更新时间</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    加载中...
                  </TableCell>
                </TableRow>
              ) : !usageData || usageData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    暂无使用数据
                  </TableCell>
                </TableRow>
              ) : (
                usageData.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-mono text-sm">
                      {item.textbook_id.slice(0, 8)}...
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {item.tenant_id.slice(0, 8)}...
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{item.access_count}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{item.download_count}</Badge>
                    </TableCell>
                    <TableCell>
                      {formatDate(item.created_at)}
                    </TableCell>
                    <TableCell>
                      {formatDate(item.updated_at)}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextbookUsageAnalytics;
