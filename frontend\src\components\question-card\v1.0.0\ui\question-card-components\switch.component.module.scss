@use '../global.module.scss' as base;

.switch-root {
  @extend .unset;
  @extend .border-default;

  cursor: pointer;
  width: 40px;
  height: 24px;
  border-radius: 99999px;
  position: relative;
  box-shadow: 0 2px 10px var(--black-a7);
  background-color: base.$background-color;
  transition: all 200ms;
  margin: 5px 0;

  &[data-state='checked'] {
    background-color: #171717;
  }

  .switch-thumb {
    display: block;
    width: 16px;
    height: 16px;
    background-color: #e4e7ed;
    border-radius: 9999px;
    box-shadow: 0 2px 2px var(--black-a7);
    transition: transform 200ms;
    transform: translateX(2px);

    &[data-state='checked'] {
      transform: translateX(19px);
    }
  }
}
