export interface TextbookTenantAuthorization {
  id: string;
  textbook_id: string;
  tenant_id: string;
  tenant_name?: string; // 租户名称
  granted_by: string;
  granted_by_name?: string; // 授权人姓名
  granted_at: string;
  expires_at: string | null;
  status: 'active' | 'suspended' | 'revoked';
  permissions: {
    read: boolean;
    download: boolean;
    distribute: boolean;
  };
  notes: string | null;
  created_at: string;
  updated_at: string;
}

export interface TextbookAuthorizationAudit {
  id: string;
  authorization_id: string | null;
  textbook_id: string;
  tenant_id: string;
  tenant_name?: string; // 租户名称
  action: 'granted' | 'revoked' | 'suspended' | 'renewed' | 'permissions_changed' | 'deleted';
  old_status: string | null;
  new_status: string | null;
  old_permissions: any | null;
  new_permissions: any | null;
  performed_by: string;
  performed_by_name?: string; // 操作人姓名
  reason: string | null;
  created_at: string;
}

export interface TextbookTenantUsage {
  id: string;
  textbook_id: string;
  tenant_id: string;
  tenant_name?: string; // 租户名称
  access_count: number;
  download_count: number;
  last_accessed_at: string | null;
  last_downloaded_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface CreateAuthorizationRequest {
  textbook_id: string;
  tenant_id: string;
  expires_at?: string;
  permissions?: {
    read: boolean;
    download: boolean;
    distribute: boolean;
  };
  notes?: string;
}

export interface UpdateAuthorizationRequest {
  status?: 'active' | 'suspended' | 'revoked';
  expires_at?: string;
  permissions?: {
    read: boolean;
    download: boolean;
    distribute: boolean;
  };
  notes?: string;
  reason?: string;
}

export interface AuthorizationQuery {
  page?: number;
  page_size?: number;
  textbook_id?: string;
  tenant_id?: string;
  status?: string;
  search?: string;
}

export interface AuthorizationsResponse {
  authorizations: TextbookTenantAuthorization[];
  total: number;
}

export interface TenantTextbooksResponse {
  textbooks: TextbookTenantAuthorization[];
  total: number;
  tenant_id: string;
}

export interface PermissionCheckResponse {
  textbook_id: string;
  tenant_id: string;
  permission: string;
  has_permission: boolean;
}

export interface AuditHistoryResponse {
  audit_logs: TextbookAuthorizationAudit[];
  total: number;
  authorization_id: string;
}