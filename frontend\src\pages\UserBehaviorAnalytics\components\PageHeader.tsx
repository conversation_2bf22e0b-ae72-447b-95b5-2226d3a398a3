import React from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RefreshCw } from 'lucide-react';

interface PageHeaderProps {
  timeRange: string;
  onTimeRangeChange: (value: string) => void;
  onRefresh: () => void;
  lastUpdateTime?: Date | null;
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  timeRange,
  onTimeRangeChange,
  onRefresh,
  lastUpdateTime
}) => {
  return (
    <div className="flex justify-between items-center">
      <div>
        <h1 className="text-3xl font-bold">用户行为分析</h1>
        <p className="text-muted-foreground">实时监控用户活动和在线状态</p>
        {lastUpdateTime && (
          <div className="flex items-center text-sm text-muted-foreground mt-2">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            <span>最后更新: {lastUpdateTime.toLocaleTimeString('zh-CN')}</span>
          </div>
        )}
      </div>
      <div className="flex items-center space-x-4">
        <Select value={timeRange} onValueChange={onTimeRangeChange}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="选择时间范围" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">今日</SelectItem>
            <SelectItem value="yesterday">昨日</SelectItem>
            <SelectItem value="week">近7天</SelectItem>
            <SelectItem value="month">近30天</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" onClick={onRefresh} className="flex items-center">
          <RefreshCw className="h-4 w-4 mr-2" />
          刷新数据
        </Button>
      </div>
    </div>
  );
}; 