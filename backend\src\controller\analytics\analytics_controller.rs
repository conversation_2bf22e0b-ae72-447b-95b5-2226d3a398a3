use axum::{
    extract::{Query, State},
    http::HeaderMap,
    response::J<PERSON>,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc, NaiveDate};

use crate::service::analytics::{
    analytics_dashboard_service::AnalyticsDashboardService,
    user_behavior_service::UserBehaviorService,
    online_users_service::OnlineUsersService,
};
use crate::model::analytics::analytics_dto::*;
use crate::model::analytics::user_behavior_model::BehaviorLogQueryParams;
use crate::model::analytics::online_users_model::OnlineUserQueryParams;
use crate::middleware::auth_middleware::AuthExtractor;
use crate::utils::api_response::ApiResponse;
use crate::web_server::AppState;

/// 分析查询参数
#[derive(Debug, Deserialize)]
pub struct AnalyticsQuery {
    pub start_date: Option<NaiveDate>,
    pub end_date: Option<NaiveDate>,
    pub days: Option<i32>,
    pub module: Option<String>,
}

/// 从请求头中提取租户名称
fn extract_tenant_name_from_headers(headers: &HeaderMap) -> Result<String, &'static str> {
    headers
        .get("x-tenant-id")
        .and_then(|value| value.to_str().ok())
        .map(|s| s.to_string())
        .ok_or("Missing or invalid x-tenant-id header")
}

/// 创建分析相关的路由
pub fn create_router() -> Router<AppState> {
    Router::new()
        // 综合分析看板
        .route("/dashboard", get(get_analytics_dashboard))
        .route("/overview", get(get_analytics_overview))
        .route("/real-time", get(get_real_time_metrics))
        
        // 用户行为分析
        .route("/behavior/logs", get(get_behavior_logs))
        .route("/behavior/analytics", get(get_behavior_analytics))
        .route("/behavior/trends", get(get_behavior_trends))
        .route("/behavior/user/pattern", get(get_user_behavior_pattern))
        
        // 在线用户管理
        .route("/online/count", get(get_online_count))
        .route("/online/users", get(get_online_users))
        .route("/online/stats", get(get_online_stats))
        .route("/online/cleanup", post(cleanup_inactive_users))
}

/// 获取综合分析看板数据
pub async fn get_analytics_dashboard(
    State(state): State<AppState>,
    headers: HeaderMap,
    Query(query): Query<AnalyticsQuery>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<AnalyticsDashboard>, ApiResponse<String>> {
    let _tenant_name = match extract_tenant_name_from_headers(&headers) {
        Ok(name) => name,
        Err(err) => return Err(ApiResponse::error(err.to_string(), None)),
    };
    
    let service = AnalyticsDashboardService::new(state.db);
    let tenant_id = context.tenant_links.first().map(|link| link.tenant_id);
    
    let params = AnalyticsQueryParams {
        tenant_id,
        start_date: query.start_date,
        end_date: query.end_date,
        module: query.module,
        user_id: None,
        device_type: None,
        include_real_time: Some(true),
        include_trends: Some(true),
        trend_days: query.days,
    };

    match service.get_overview(tenant_id, &context).await {
        Ok(overview) => {
            let dashboard = AnalyticsDashboard {
                overview,
                real_time_metrics: service.get_real_time_metrics(tenant_id, &context).await.unwrap_or_else(|_| {
                    RealTimeMetrics {
                        current_online: 0,
                        active_last_5min: 0,
                        active_last_15min: 0,
                        active_last_hour: 0,
                        actions_last_hour: 0,
                        avg_response_time_ms: 0.0,
                        error_rate_percent: 0.0,
                        new_sessions_last_hour: 0,
                    }
                }),
                behavior_analysis: BehaviorAnalysisData {
                    most_active_modules: vec![],
                    popular_pages: vec![],
                    hourly_activity: vec![],
                    device_usage: vec![],
                    user_journey: vec![],
                },
                online_user_analysis: OnlineUserAnalysisData {
                    distribution_by_status: vec![],
                    distribution_by_module: vec![],
                    distribution_by_device: vec![],
                    distribution_by_tenant: vec![],
                    session_duration_ranges: vec![],
                },
                trends: TrendsData {
                    daily_active_users: vec![],
                    daily_sessions: vec![],
                    daily_page_views: vec![],
                    hourly_concurrent_users: vec![],
                    module_usage_trends: vec![],
                },
            };
            
            Ok(ApiResponse::success(dashboard, Some("获取分析看板数据成功".to_string())))
        },
        Err(e) => Err(ApiResponse::error(e.to_string(), None)),
    }
}

/// 获取分析概览数据
pub async fn get_analytics_overview(
    State(state): State<AppState>,
    headers: HeaderMap,
    Query(query): Query<AnalyticsQuery>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<AnalyticsOverview>, ApiResponse<String>> {
    let _tenant_name = match extract_tenant_name_from_headers(&headers) {
        Ok(name) => name,
        Err(err) => return Err(ApiResponse::error(err.to_string(), None)),
    };

    let service = AnalyticsDashboardService::new(state.db);
    let tenant_id = context.tenant_links.first().map(|link| link.tenant_id);

    match service.get_overview(tenant_id, &context).await {
        Ok(overview) => Ok(ApiResponse::success(overview, None)),
        Err(err) => Err(ApiResponse::error(err.to_string(), None)),
    }
}

/// 获取实时指标
pub async fn get_real_time_metrics(
    State(state): State<AppState>,
    headers: HeaderMap,
    Query(query): Query<AnalyticsQuery>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<RealTimeMetrics>, ApiResponse<String>> {
    let _tenant_name = match extract_tenant_name_from_headers(&headers) {
        Ok(name) => name,
        Err(err) => return Err(ApiResponse::error(err.to_string(), None)),
    };

    let service = AnalyticsDashboardService::new(state.db);
    let tenant_id = context.tenant_links.first().map(|link| link.tenant_id);

    match service.get_real_time_metrics(tenant_id, &context).await {
        Ok(metrics) => Ok(ApiResponse::success(metrics, None)),
        Err(err) => Err(ApiResponse::error(err.to_string(), None)),
    }
}

/// 获取用户行为日志
pub async fn get_behavior_logs(
    State(state): State<AppState>,
    headers: HeaderMap,
    Query(params): Query<BehaviorLogQueryParams>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<Vec<crate::model::analytics::user_behavior_model::UserBehaviorLog>>, ApiResponse<String>> {
    let _tenant_name = match extract_tenant_name_from_headers(&headers) {
        Ok(name) => name,
        Err(err) => return Err(ApiResponse::error(err.to_string(), None)),
    };

    let service = UserBehaviorService::new(state.db);
    
    match service.get_behavior_logs(&params, &context).await {
        Ok(logs) => Ok(ApiResponse::success(logs, None)),
        Err(err) => Err(ApiResponse::error(err.to_string(), None)),
    }
}

/// 获取用户行为分析数据
pub async fn get_behavior_analytics(
    State(state): State<AppState>,
    headers: HeaderMap,
    Query(query): Query<AnalyticsQuery>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<crate::model::analytics::user_behavior_model::BehaviorAnalyticsData>, ApiResponse<String>> {
    let _tenant_name = match extract_tenant_name_from_headers(&headers) {
        Ok(name) => name,
        Err(err) => return Err(ApiResponse::error(err.to_string(), None)),
    };

    let service = UserBehaviorService::new(state.db);
    let tenant_id = context.tenant_links.first().map(|link| link.tenant_id);

    let start_date = query.start_date
        .map(|d| d.and_hms_opt(0, 0, 0).unwrap().and_utc())
        .unwrap_or_else(|| Utc::now() - chrono::Duration::days(7));
    
    let end_date = query.end_date
        .map(|d| d.and_hms_opt(23, 59, 59).unwrap().and_utc())
        .unwrap_or_else(|| Utc::now());

    match service.get_behavior_analytics(tenant_id, start_date, end_date, &context).await {
        Ok(analytics) => Ok(ApiResponse::success(analytics, None)),
        Err(err) => Err(ApiResponse::error(err.to_string(), None)),
    }
}

/// 获取用户行为趋势
pub async fn get_behavior_trends(
    State(state): State<AppState>,
    headers: HeaderMap,
    Query(query): Query<AnalyticsQuery>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<Vec<crate::model::analytics::user_behavior_model::UserBehaviorTrend>>, ApiResponse<String>> {
    let _tenant_name = match extract_tenant_name_from_headers(&headers) {
        Ok(name) => name,
        Err(err) => return Err(ApiResponse::error(err.to_string(), None)),
    };

    let service = UserBehaviorService::new(state.db);
    let tenant_id = context.tenant_links.first().map(|link| link.tenant_id);

    let days = query.days.unwrap_or(7);
    match service.get_behavior_trends(tenant_id, days, &context).await {
        Ok(trends) => Ok(ApiResponse::success(trends, None)),
        Err(err) => Err(ApiResponse::error(err.to_string(), None)),
    }
}

/// 获取特定用户行为模式
pub async fn get_user_behavior_pattern(
    State(state): State<AppState>,
    headers: HeaderMap,
    Query(params): Query<std::collections::HashMap<String, String>>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<crate::service::analytics::user_behavior_service::UserBehaviorPattern>, ApiResponse<String>> {
    let _tenant_name = match extract_tenant_name_from_headers(&headers) {
        Ok(name) => name,
        Err(err) => return Err(ApiResponse::error(err.to_string(), None)),
    };

    let user_id = params.get("user_id")
        .and_then(|s| s.parse::<Uuid>().ok())
        .ok_or_else(|| ApiResponse::error("Invalid user_id parameter".to_string(), None))?;
        
    let days = params.get("days")
        .and_then(|s| s.parse::<i32>().ok())
        .unwrap_or(30);

    let service = UserBehaviorService::new(state.db);
    let tenant_id = context.tenant_links.first().map(|link| link.tenant_id);
    
    match service.analyze_user_patterns(user_id, tenant_id, days, &context).await {
        Ok(pattern) => Ok(ApiResponse::success(pattern, None)),
        Err(err) => Err(ApiResponse::error(err.to_string(), None)),
    }
}

/// 获取在线用户数量
pub async fn get_online_count(
    State(state): State<AppState>,
    headers: HeaderMap,
    Query(query): Query<AnalyticsQuery>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<i64>, ApiResponse<String>> {
    let _tenant_name = match extract_tenant_name_from_headers(&headers) {
        Ok(name) => name,
        Err(err) => return Err(ApiResponse::error(err.to_string(), None)),
    };

    let service = OnlineUsersService::new(state.db);
    let tenant_id = context.tenant_links.first().map(|link| link.tenant_id);

    match service.get_online_count(tenant_id, &context).await {
        Ok(count) => Ok(ApiResponse::success(count, None)),
        Err(err) => Err(ApiResponse::error(err.to_string(), None)),
    }
}

/// 获取在线用户列表
pub async fn get_online_users(
    State(state): State<AppState>,
    headers: HeaderMap,
    Query(params): Query<OnlineUserQueryParams>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<Vec<crate::model::analytics::online_users_model::OnlineUserActivity>>, ApiResponse<String>> {
    let _tenant_name = match extract_tenant_name_from_headers(&headers) {
        Ok(name) => name,
        Err(err) => return Err(ApiResponse::error(err.to_string(), None)),
    };

    let service = OnlineUsersService::new(state.db);
    
    match service.get_online_users(&params, &context).await {
        Ok(users) => Ok(ApiResponse::success(users, None)),
        Err(err) => Err(ApiResponse::error(err.to_string(), None)),
    }
}

/// 获取在线用户统计信息
pub async fn get_online_stats(
    State(state): State<AppState>,
    headers: HeaderMap,
    Query(query): Query<AnalyticsQuery>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<crate::model::analytics::online_users_model::OnlineUserStats>, ApiResponse<String>> {
    let _tenant_name = match extract_tenant_name_from_headers(&headers) {
        Ok(name) => name,
        Err(err) => return Err(ApiResponse::error(err.to_string(), None)),
    };

    let service = OnlineUsersService::new(state.db);
    let tenant_id = context.get_tenant_id_from_headers(headers);

    match service.get_online_stats(tenant_id, &context).await {
        Ok(stats) => Ok(ApiResponse::success(stats, None)),
        Err(err) => Err(ApiResponse::error(err.to_string(), None)),
    }
}

/// 清理不活跃的用户
pub async fn cleanup_inactive_users(
    State(state): State<AppState>,
    headers: HeaderMap,
    Query(query): Query<AnalyticsQuery>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<u64>, ApiResponse<String>> {
    let _tenant_name = match extract_tenant_name_from_headers(&headers) {
        Ok(name) => name,
        Err(err) => return Err(ApiResponse::error(err.to_string(), None)),
    };

    let service = OnlineUsersService::new(state.db);
    
    // 默认清理15分钟内未活动的用户
    let inactive_minutes = 15;
    
    match service.cleanup_inactive_users(inactive_minutes).await {
        Ok(count) => Ok(ApiResponse::success(count, None)),
        Err(err) => Err(ApiResponse::error(err.to_string(), None)),
    }
} 