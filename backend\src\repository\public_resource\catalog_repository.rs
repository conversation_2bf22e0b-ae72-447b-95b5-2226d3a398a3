use sqlx::{<PERSON><PERSON><PERSON>, QueryBuilder, Transaction};
use crate::model::public_resource::catalog::Catalog;

pub struct CatalogRepository;
impl CatalogRepository {
    pub async fn bulk_insert_or_update<'e>(executor: &mut Transaction<'e, Postgres>, catalogs: Vec<Catalog>) -> anyhow::Result<()> {
        let mut builder = QueryBuilder::new(r#"INSERT INTO public.catalogs
        (id, book_id, parent_id, serial, level, title, section_id, updated_at, origin_id, is_deleted) "#);
        builder.push_values(catalogs, |mut b, catalog| {
            b.push_bind(catalog.id)
                .push_bind(catalog.book_id)
                .push_bind(catalog.parent_id)
                .push_bind(catalog.serial)
                .push_bind(catalog.level)
                .push_bind(catalog.title)
                .push_bind(catalog.section_id)
                .push_bind(catalog.updated_at)
                .push_bind(catalog.origin_id)
                .push_bind(catalog.is_deleted);
        });
        builder.push(r#"ON CONFLICT (origin_id) DO UPDATE SET
        id = EXCLUDED.id,
        book_id = EXCLUDED.book_id,
        parent_id = EXCLUDED.parent_id,
        serial = EXCLUDED.serial,
        level = EXCLUDED.level,
        title = EXCLUDED.title,
        section_id = EXCLUDED.section_id,
        updated_at = EXCLUDED.updated_at,
        is_deleted = EXCLUDED.is_deleted"#);
        builder.build().execute(&mut **executor).await?;
        Ok(())
    }
}