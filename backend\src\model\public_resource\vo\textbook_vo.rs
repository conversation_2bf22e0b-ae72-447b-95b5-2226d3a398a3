use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::model::public_resource::ResourceVersion;
use crate::model::textbooks::Textbook;
use crate::utils::date_time;

#[derive(Deserialize, Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct BookJson {
    pub id: String,
    pub title: String,
    pub subject_code: Option<String>,
    pub grade_level_code: Option<String>,
    pub publisher: Option<String>,
    pub distributor: Option<String>,
    pub year: Option<i32>,
    pub cover_path: Option<String>,
    pub isbn: Option<String>,
    pub edition: Option<String>,
    pub printing_version: Option<String>,
    pub authors: Option<Vec<String>>,
    pub summary: Option<String>,
    #[serde(deserialize_with = "date_time::deserialize")]
    pub update_at: DateTime<Utc>,
}

impl From<BookJson> for Textbook {
    fn from(value: <PERSON><PERSON><PERSON>) -> Self {
        let origin_id = value.id.parse().ok();
        Self {
            id: Uuid::new_v4(),
            title: value.title,
            subject_id: None,
            grade_level_id: None,
            publisher: value.publisher,
            publication_year: value.year,
            cover_path: value.cover_path,
            isbn: value.isbn,
            version: None,
            status: None,
            creator_id: None,
            created_at: None,
            updated_at: Some(value.update_at),
            subject_code: value.subject_code,
            grade_level_code: value.grade_level_code,
            distributor: value.distributor,
            edition: value.edition,
            printing_version: value.printing_version,
            authors: value.authors,
            summary: value.summary,
            resource_version: Some(ResourceVersion::V2),
            origin_id,
            is_deleted: false,
        }
    }
}