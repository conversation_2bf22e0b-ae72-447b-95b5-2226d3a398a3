import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Users, Clock } from 'lucide-react';
import type { RealTimeMetrics } from '@/services/analyticsApi';

interface RealTimeMetricsCardsProps {
  realTimeMetrics: RealTimeMetrics;
}

export const RealTimeMetricsCards: React.FC<RealTimeMetricsCardsProps> = ({
  realTimeMetrics
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">实时在线</CardTitle>
          <Users className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{realTimeMetrics.current_online}</div>
          <p className="text-xs text-muted-foreground">
            过去1小时: {realTimeMetrics.active_last_hour}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">响应时间</CardTitle>
          <Clock className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{realTimeMetrics.avg_response_time_ms.toFixed(0)}ms</div>
          <p className="text-xs text-muted-foreground">
            错误率: {realTimeMetrics.error_rate_percent.toFixed(1)}%
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">活跃用户 (5分钟)</CardTitle>
          <Users className="h-4 w-4 text-yellow-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{realTimeMetrics.active_last_5min}</div>
          <p className="text-xs text-muted-foreground">
            15分钟: {realTimeMetrics.active_last_15min}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">新建会话</CardTitle>
          <Users className="h-4 w-4 text-indigo-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{realTimeMetrics.new_sessions_last_hour}</div>
          <p className="text-xs text-muted-foreground">
            操作次数: {realTimeMetrics.actions_last_hour}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}; 