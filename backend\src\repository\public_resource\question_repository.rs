use std::collections::HashMap;
use sqlx::{Pg<PERSON><PERSON>, <PERSON>gres, QueryBuilder, Transaction};
use uuid::Uuid;
use crate::model::public_resource::question::Question;

pub struct QuestionRepository;

impl QuestionRepository {
    pub async fn fetch_questions(pool: &PgPool, question_ids: Vec<Uuid>) -> anyhow::Result<Vec<Question>> {
        let mut builder = QueryBuilder::new("SELECT * FROM public.questions WHERE id = ANY(");
        builder.push_bind(question_ids).push(")");
        let questions = builder.build_query_as().fetch_all(pool).await?;
        Ok(questions)
    }
    pub async fn bulk_insert_or_update<'e>(executor: &mut Transaction<'e, Postgres>, questions: Vec<Question>) -> anyhow::Result<HashMap<i64, Uuid>> {
        let mut builder = QueryBuilder::new(r#"INSERT INTO public.questions
        (question_type_code, subject_code, items, updated_at, origin_id, is_deleted) "#);
        builder.push_values(questions, |mut b, question| {
            b.push_bind(question.question_type_code)
            .push_bind(question.subject_code)
            .push_bind(question.items)
            .push_bind(question.updated_at)
            .push_bind(question.origin_id)
            .push_bind(question.is_deleted);
        });
        builder.push(r#"ON CONFLICT (origin_id) DO UPDATE SET
        question_type_code = EXCLUDED.question_type_code,
        subject_code = EXCLUDED.subject_code,
        items = EXCLUDED.items,
        updated_at = EXCLUDED.updated_at,
        is_deleted = EXCLUDED.is_deleted
        RETURNING id, origin_id"#);
        let list: Vec<(Uuid, i64)> = builder.build_query_as().fetch_all(&mut **executor).await?;
        let ret: HashMap<i64, Uuid> = list.into_iter().map(|(id, origin_id)| (origin_id, id)).collect();
        Ok(ret)
    }
}