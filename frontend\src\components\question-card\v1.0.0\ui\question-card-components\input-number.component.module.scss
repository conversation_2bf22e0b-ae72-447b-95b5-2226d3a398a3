@use '../global.module.scss' as base;

.input-number-root {
  @extend .unset;
  @extend .border-default;
  @extend .hover-default;
  position: relative;
  width: 120px;
  line-height: 32px;
  height: 32px;
  overflow: scroll;
  scrollbar-width: none;
  overscroll-behavior: contain;
  -ms-scroll-chaining: contain;
  .input-number-contain {
    position: sticky;
    top: 0;
    left: 0;
    height: 30px;
    line-height: 30px;
    border-radius: base.$border-radius;
    width: 100%;
    .input {
      @extend .unset;
      @extend .hover-default;
      @extend .disabled;
      padding: 0 8px;
      height: 30px;
      line-height: 30px;
      border-radius: base.$border-radius;
      width: 0;
      flex-grow: 1;
      cursor: pointer;
    }

    .suffix {
      white-space: nowrap;
      padding: 0 5px;
    }
  }
  .scroll-hidden {
    height: 40px;
  }
}
