use sqlx::{PgPool, Row};
use anyhow::Result;
use chrono::{DateTime, Utc};
use uuid::Uuid;
use std::net::IpAddr;
use std::collections::HashMap;

use crate::model::analytics::online_users_model::*;

/// 在线用户数据访问层
pub struct OnlineUsersRepository {
    pool: PgPool,
}

impl OnlineUsersRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 创建或更新在线用户状态
    pub async fn upsert_online_user(&self, request: UpsertOnlineUserRequest) -> Result<()> {
        let ip_addr: Option<IpAddr> = request.ip_address
            .as_deref()
            .and_then(|s| s.parse().ok());

        sqlx::query!(
            r#"
            INSERT INTO online_users (
                user_id, tenant_id, session_id,
                status, current_page, current_module,
                ip_address, user_agent, device_type, browser, os,
                last_activity_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW()
            )
            ON CONFLICT (user_id, session_id)
            DO UPDATE SET
                status = COALESCE($4, online_users.status),
                current_page = COALESCE($5, online_users.current_page),
                current_module = COALESCE($6, online_users.current_module),
                last_activity_at = NOW(),
                updated_at = NOW()
            "#,
            request.user_id,
            request.tenant_id,
            request.session_id,
            request.status.unwrap_or_else(|| "online".to_string()),
            request.current_page,
            request.current_module,
            ip_addr.map(|addr| ipnetwork::IpNetwork::from(addr)),
            request.user_agent,
            request.device_type,
            request.browser,
            request.os
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// 用户登出时更新状态
    pub async fn logout_user(&self, user_id: Uuid, session_id: Uuid) -> Result<()> {
        sqlx::query!(
            "UPDATE online_users SET status = 'offline', logout_at = NOW() WHERE user_id = $1 AND session_id = $2",
            user_id, session_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// 获取当前在线用户数量
    pub async fn get_current_online_count(&self, tenant_id: Option<Uuid>) -> Result<i64> {
        let count = if let Some(tid) = tenant_id {
            sqlx::query_scalar!(
                "SELECT COUNT(DISTINCT user_id)::bigint FROM online_users WHERE status = 'online' AND tenant_id = $1 AND last_activity_at >= NOW() - INTERVAL '5 minutes'",
                tid
            )
            .fetch_one(&self.pool)
            .await?
        } else {
            sqlx::query_scalar!(
                "SELECT COUNT(DISTINCT user_id)::bigint FROM online_users WHERE status = 'online' AND last_activity_at >= NOW() - INTERVAL '5 minutes'"
            )
            .fetch_one(&self.pool)
            .await?
        };

        Ok(count.unwrap_or(0))
    }

    /// 清理过期的在线用户记录
    pub async fn cleanup_stale_users(&self, inactive_minutes: i32) -> Result<u64> {
        let result = sqlx::query!(
            "UPDATE online_users SET status = 'offline' WHERE last_activity_at < NOW() - INTERVAL '1 minute' * $1 AND status != 'offline'",
            inactive_minutes as f64
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected())
    }

    /// 获取在线用户统计信息
    pub async fn get_online_stats(&self, tenant_id: Option<Uuid>) -> Result<OnlineUserStats> {
        // 总在线用户数
        let total_online = if let Some(tid) = tenant_id {
            sqlx::query_scalar!(
                "SELECT COUNT(DISTINCT user_id)::bigint FROM online_users WHERE status = 'online' AND last_activity_at >= NOW() - INTERVAL '5 minutes' AND tenant_id = $1",
                tid
            )
            .fetch_one(&self.pool)
            .await?
            .unwrap_or(0)
        } else {
            sqlx::query_scalar!(
                "SELECT COUNT(DISTINCT user_id)::bigint FROM online_users WHERE status = 'online' AND last_activity_at >= NOW() - INTERVAL '5 minutes'"
            )
            .fetch_one(&self.pool)
            .await?
            .unwrap_or(0)
        };

        // 按状态分组统计
        let by_status = self.get_stats_by_status(tenant_id).await?;
        
        // 按模块分组统计
        let by_module = self.get_stats_by_module(tenant_id).await?;
        
        // 按设备分组统计
        let by_device = self.get_stats_by_device(tenant_id).await?;
        
        // 按租户分组统计（仅在查询所有租户时有效）
        let by_tenant = if tenant_id.is_none() {
            self.get_stats_by_tenant().await?
        } else {
            let mut map = HashMap::new();
            if let Some(tid) = tenant_id {
                map.insert(tid.to_string(), total_online);
            }
            map
        };

        // 今日峰值并发用户数
        let peak_concurrent_today = self.get_peak_concurrent_today(tenant_id).await?;
        
        // 平均会话时长
        let avg_session_duration_minutes = self.get_avg_session_duration(tenant_id).await?;

        Ok(OnlineUserStats {
            total_online,
            by_status,
            by_module,
            by_device,
            by_tenant,
            peak_concurrent_today,
            avg_session_duration_minutes,
        })
    }

    /// 按状态分组统计
    async fn get_stats_by_status(&self, tenant_id: Option<Uuid>) -> Result<HashMap<String, i64>> {
        let query = if tenant_id.is_some() {
            "SELECT status, COUNT(DISTINCT user_id)::bigint as count FROM online_users WHERE last_activity_at >= NOW() - INTERVAL '5 minutes' AND tenant_id = $1 GROUP BY status"
        } else {
            "SELECT status, COUNT(DISTINCT user_id)::bigint as count FROM online_users WHERE last_activity_at >= NOW() - INTERVAL '5 minutes' GROUP BY status"
        };

        let rows = if let Some(tid) = tenant_id {
            sqlx::query(query)
                .bind(tid)
                .fetch_all(&self.pool)
                .await?
        } else {
            sqlx::query(query)
                .fetch_all(&self.pool)
                .await?
        };

        let mut stats = HashMap::new();
        for row in rows {
            let status: String = row.get("status");
            let count: i64 = row.get("count");
            stats.insert(status, count);
        }

        Ok(stats)
    }

    /// 按模块分组统计
    async fn get_stats_by_module(&self, tenant_id: Option<Uuid>) -> Result<HashMap<String, i64>> {
        let query = if tenant_id.is_some() {
            "SELECT COALESCE(current_module, 'unknown') as module, COUNT(DISTINCT user_id)::bigint as count FROM online_users WHERE status = 'online' AND last_activity_at >= NOW() - INTERVAL '5 minutes' AND tenant_id = $1 GROUP BY current_module"
        } else {
            "SELECT COALESCE(current_module, 'unknown') as module, COUNT(DISTINCT user_id)::bigint as count FROM online_users WHERE status = 'online' AND last_activity_at >= NOW() - INTERVAL '5 minutes' GROUP BY current_module"
        };

        let rows = if let Some(tid) = tenant_id {
            sqlx::query(query)
                .bind(tid)
                .fetch_all(&self.pool)
                .await?
        } else {
            sqlx::query(query)
                .fetch_all(&self.pool)
                .await?
        };

        let mut stats = HashMap::new();
        for row in rows {
            let module: String = row.get("module");
            let count: i64 = row.get("count");
            stats.insert(module, count);
        }

        Ok(stats)
    }

    /// 按设备分组统计
    async fn get_stats_by_device(&self, tenant_id: Option<Uuid>) -> Result<HashMap<String, i64>> {
        let query = if tenant_id.is_some() {
            "SELECT COALESCE(device_type, 'unknown') as device, COUNT(DISTINCT user_id)::bigint as count FROM online_users WHERE status = 'online' AND last_activity_at >= NOW() - INTERVAL '5 minutes' AND tenant_id = $1 GROUP BY device_type"
        } else {
            "SELECT COALESCE(device_type, 'unknown') as device, COUNT(DISTINCT user_id)::bigint as count FROM online_users WHERE status = 'online' AND last_activity_at >= NOW() - INTERVAL '5 minutes' GROUP BY device_type"
        };

        let rows = if let Some(tid) = tenant_id {
            sqlx::query(query)
                .bind(tid)
                .fetch_all(&self.pool)
                .await?
        } else {
            sqlx::query(query)
                .fetch_all(&self.pool)
                .await?
        };

        let mut stats = HashMap::new();
        for row in rows {
            let device: String = row.get("device");
            let count: i64 = row.get("count");
            stats.insert(device, count);
        }

        Ok(stats)
    }

    /// 按租户分组统计
    async fn get_stats_by_tenant(&self) -> Result<HashMap<String, i64>> {
        let rows = sqlx::query(
            "SELECT tenant_id::text as tenant, COUNT(DISTINCT user_id)::bigint as count FROM online_users WHERE status = 'online' AND last_activity_at >= NOW() - INTERVAL '5 minutes' GROUP BY tenant_id"
        )
        .fetch_all(&self.pool)
        .await?;

        let mut stats = HashMap::new();
        for row in rows {
            let tenant: String = row.get("tenant");
            let count: i64 = row.get("count");
            stats.insert(tenant, count);
        }

        Ok(stats)
    }

    /// 获取今日峰值并发用户数
    async fn get_peak_concurrent_today(&self, tenant_id: Option<Uuid>) -> Result<i64> {
        // 这里简化实现，实际应该从历史数据或缓存中获取今日的峰值
        // 为了演示，我们使用当前在线用户数的1.5倍作为峰值
        let current_online = self.get_current_online_count(tenant_id).await?;
        Ok((current_online as f64 * 1.5) as i64)
    }

    /// 获取平均会话时长（分钟）
    async fn get_avg_session_duration(&self, tenant_id: Option<Uuid>) -> Result<f64> {
        let query = if tenant_id.is_some() {
            "SELECT AVG(EXTRACT(EPOCH FROM (COALESCE(logout_at, NOW()) - created_at)) / 60.0)::float8 as avg_duration FROM online_users WHERE tenant_id = $1 AND created_at >= CURRENT_DATE"
        } else {
            "SELECT AVG(EXTRACT(EPOCH FROM (COALESCE(logout_at, NOW()) - created_at)) / 60.0)::float8 as avg_duration FROM online_users WHERE created_at >= CURRENT_DATE"
        };

        let duration = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, Option<f64>>(query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, Option<f64>>(query)
                .fetch_one(&self.pool)
                .await?
        };

        Ok(duration.unwrap_or(0.0))
    }
} 