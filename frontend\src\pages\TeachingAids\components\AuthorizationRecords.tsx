import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  Filter, 
  Eye, 
  Download, 
  Share2, 
  Calendar, 
  User, 
  Building, 
  MoreHorizontal,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { toast } from 'sonner';
import textbookAuthorizationApi from '@/services/textbookAuthorizationApi';
import type { 
  TextbookTenantAuthorization, 
  AuthorizationQuery,
  TextbookTenantUsage,
  TextbookAuthorizationAudit
} from '@/types/textbookAuthorization';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

// Configure dayjs
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

interface AuthorizationRecordsProps {
  /** 可选：特定教辅书ID，如果提供则只显示该教辅书的授权记录 */
  textbookId?: string;
  /** 可选：特定租户ID，如果提供则只显示该租户的授权记录 */
  tenantId?: string;
  /** 组件标题 */
  title?: string;
  /** 是否显示搜索和过滤功能 */
  showFilters?: boolean;
  /** 是否显示操作按钮 */
  showActions?: boolean;
  /** 每页显示数量 */
  pageSize?: number;
  /** 高度限制 */
  maxHeight?: string;
}

const AuthorizationRecords: React.FC<AuthorizationRecordsProps> = ({
  textbookId,
  tenantId,
  title = '授权记录',
  showFilters = true,
  showActions = true,
  pageSize = 10,
  maxHeight = '600px'
}) => {
  const [authorizations, setAuthorizations] = useState<TextbookTenantAuthorization[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  
  // 过滤和搜索状态
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  // 详情对话框状态
  const [selectedAuthorization, setSelectedAuthorization] = useState<TextbookTenantAuthorization | null>(null);
  const [usageData, setUsageData] = useState<TextbookTenantUsage | null>(null);
  const [auditHistory, setAuditHistory] = useState<TextbookAuthorizationAudit[]>([]);
  const [detailsLoading, setDetailsLoading] = useState(false);

  // 获取授权记录
  const fetchAuthorizations = async (page: number = 1) => {
    try {
      setLoading(true);
      const query: AuthorizationQuery = {
        page,
        page_size: pageSize,
        textbook_id: textbookId,
        tenant_id: tenantId,
        status: statusFilter === 'all' ? undefined : statusFilter,
        search: searchTerm || undefined,
      };

      const response = await textbookAuthorizationApi.getAuthorizations(query);
      setAuthorizations(response.authorizations);
      setTotal(response.total);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching authorizations:', error);
      toast.error('获取授权记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取授权详情（使用统计和审计历史）
  const fetchAuthorizationDetails = async (authorization: TextbookTenantAuthorization) => {
    try {
      setDetailsLoading(true);
      setSelectedAuthorization(authorization);
      
      // 并行获取使用统计和审计历史
      const [usageResponse, auditResponse] = await Promise.all([
        textbookAuthorizationApi.getTextbookTenantUsage(authorization.textbook_id, authorization.tenant_id),
        textbookAuthorizationApi.getAuthorizationAuditHistory(authorization.id, 1, 20)
      ]);
      
      setUsageData(usageResponse);
      setAuditHistory(auditResponse.audit_logs);
    } catch (error) {
      console.error('Error fetching authorization details:', error);
      toast.error('获取授权详情失败');
    } finally {
      setDetailsLoading(false);
    }
  };

  // 状态徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />有效</Badge>;
      case 'suspended':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />暂停</Badge>;
      case 'revoked':
        return <Badge variant="destructive" className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />已撤销</Badge>;
      default:
        return <Badge variant="outline"><AlertCircle className="w-3 h-3 mr-1" />未知</Badge>;
    }
  };

  // 权限图标
  const getPermissionIcons = (permissions: { read: boolean; download: boolean; distribute: boolean }) => {
    return (
      <div className="flex gap-1">
        {permissions.read && <Eye className="w-4 h-4 text-blue-500" />}
        {permissions.download && <Download className="w-4 h-4 text-green-500" />}
        {permissions.distribute && <Share2 className="w-4 h-4 text-purple-500" />}
      </div>
    );
  };

  // 格式化日期
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '永久有效';
    return dayjs(dateString).format('YYYY-MM-DD HH:mm');
  };

  // 相对时间
  const getRelativeTime = (dateString: string) => {
    return dayjs(dateString).fromNow();
  };

  // 初始化和搜索/过滤变化时重新获取数据
  useEffect(() => {
    fetchAuthorizations(1);
  }, [textbookId, tenantId, searchTerm, statusFilter]);

  // 页面变化时获取数据
  useEffect(() => {
    if (currentPage > 1) {
      fetchAuthorizations(currentPage);
    }
  }, [currentPage]);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Building className="w-5 h-5" />
              {title}
            </CardTitle>
            <CardDescription>
              共 {total} 条授权记录
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchAuthorizations(currentPage)}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* 搜索和过滤 */}
        {showFilters && (
          <div className="flex gap-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索租户名称或ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="active">有效</SelectItem>
                <SelectItem value="suspended">暂停</SelectItem>
                <SelectItem value="revoked">已撤销</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {/* 授权记录卡片列表 */}
        <div className="space-y-3" style={{ maxHeight }}>
          <ScrollArea className="h-full">
            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="w-4 h-4 animate-spin mx-auto mb-2" />
                加载中...
              </div>
            ) : authorizations.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                暂无授权记录
              </div>
            ) : (
              <div className="space-y-3">
                {authorizations.map((auth) => (
                  <Card key={auth.id} className="p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                        {/* 租户信息和状态 */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Building className="w-4 h-4 text-gray-400" />
                            <div>
                              <div className="font-medium text-sm">
                                {auth.tenant_name || `租户ID: ${auth.tenant_id}`}
                              </div>
                              {auth.notes && (
                                <div className="text-xs text-gray-500 mt-1" title={auth.notes}>
                                  {auth.notes.length > 30 ? `${auth.notes.substring(0, 30)}...` : auth.notes}
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {getStatusBadge(auth.status)}
                            {showActions && (
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => fetchAuthorizationDetails(auth)}
                                  >
                                    <MoreHorizontal className="w-4 h-4" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-4xl max-h-[80vh]">
                                  <DialogHeader>
                                    <DialogTitle>授权详情</DialogTitle>
                                    <DialogDescription>
                                      {selectedAuthorization?.tenant_name || `租户 ${selectedAuthorization?.tenant_id}`} 的授权详细信息
                                    </DialogDescription>
                                  </DialogHeader>
                                  
                                  {detailsLoading ? (
                                    <div className="flex items-center justify-center py-8">
                                      <RefreshCw className="w-4 h-4 animate-spin mr-2" />
                                      加载详情中...
                                    </div>
                                  ) : selectedAuthorization && (
                                    <Tabs defaultValue="basic" className="w-full">
                                      <TabsList className="grid w-full grid-cols-3">
                                        <TabsTrigger value="basic">基本信息</TabsTrigger>
                                        <TabsTrigger value="usage">使用统计</TabsTrigger>
                                        <TabsTrigger value="history">操作历史</TabsTrigger>
                                      </TabsList>
                                      
                                      <TabsContent value="basic" className="space-y-4">
                                        <div className="grid grid-cols-2 gap-4">
                                          <div>
                                            <Label>授权ID</Label>
                                            <div className="text-sm font-mono bg-gray-100 p-2 rounded">
                                              {selectedAuthorization.id}
                                            </div>
                                          </div>
                                          <div>
                                            <Label>状态</Label>
                                            <div className="mt-1">{getStatusBadge(selectedAuthorization.status)}</div>
                                          </div>
                                          <div>
                                            <Label>授权时间</Label>
                                            <div className="text-sm">{formatDate(selectedAuthorization.granted_at)}</div>
                                          </div>
                                          <div>
                                            <Label>过期时间</Label>
                                            <div className="text-sm">{formatDate(selectedAuthorization.expires_at)}</div>
                                          </div>
                                          <div>
                                            <Label>授权人</Label>
                                            <div className="text-sm">
                                              {selectedAuthorization.granted_by_name || `用户ID: ${selectedAuthorization.granted_by}`}
                                            </div>
                                          </div>
                                          <div>
                                            <Label>权限</Label>
                                            <div className="mt-1">{getPermissionIcons(selectedAuthorization.permissions)}</div>
                                          </div>
                                        </div>
                                        {selectedAuthorization.notes && (
                                          <div>
                                            <Label>备注</Label>
                                            <div className="text-sm bg-gray-50 p-3 rounded">
                                              {selectedAuthorization.notes}
                                            </div>
                                          </div>
                                        )}
                                      </TabsContent>
                                      
                                      <TabsContent value="usage" className="space-y-4">
                                        {usageData ? (
                                          <div className="grid grid-cols-2 gap-4">
                                            <div className="text-center p-4 bg-blue-50 rounded">
                                              <div className="text-2xl font-bold text-blue-600">{usageData.access_count}</div>
                                              <div className="text-sm text-gray-600">访问次数</div>
                                            </div>
                                            <div className="text-center p-4 bg-green-50 rounded">
                                              <div className="text-2xl font-bold text-green-600">{usageData.download_count}</div>
                                              <div className="text-sm text-gray-600">下载次数</div>
                                            </div>
                                            <div>
                                              <Label>最后访问时间</Label>
                                              <div className="text-sm">
                                                {usageData.last_accessed_at ? formatDate(usageData.last_accessed_at) : '从未访问'}
                                              </div>
                                            </div>
                                            <div>
                                              <Label>最后下载时间</Label>
                                              <div className="text-sm">
                                                {usageData.last_downloaded_at ? formatDate(usageData.last_downloaded_at) : '从未下载'}
                                              </div>
                                            </div>
                                          </div>
                                        ) : (
                                          <div className="text-center py-8 text-gray-500">
                                            暂无使用统计数据
                                          </div>
                                        )}
                                      </TabsContent>
                                      
                                      <TabsContent value="history">
                                        <ScrollArea className="h-64">
                                          {auditHistory.length > 0 ? (
                                            <div className="space-y-2">
                                              {auditHistory.map((audit) => (
                                                <div key={audit.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded">
                                                  <Calendar className="w-4 h-4 mt-1 text-gray-400" />
                                                  <div className="flex-1">
                                                    <div className="flex items-center gap-2">
                                                      <Badge variant="outline">{audit.action}</Badge>
                                                      <span className="text-sm text-gray-600">
                                                        {formatDate(audit.created_at)}
                                                      </span>
                                                    </div>
                                                    <div className="text-sm mt-1">
                                                      操作人: {audit.performed_by_name || `用户ID: ${audit.performed_by}`}
                                                    </div>
                                                    {audit.reason && (
                                                      <div className="text-sm text-gray-600 mt-1">
                                                        原因: {audit.reason}
                                                      </div>
                                                    )}
                                                  </div>
                                                </div>
                                              ))}
                                            </div>
                                          ) : (
                                            <div className="text-center py-8 text-gray-500">
                                              暂无操作历史
                                            </div>
                                          )}
                                        </ScrollArea>
                                      </TabsContent>
                                    </Tabs>
                                  )}
                                </DialogContent>
                              </Dialog>
                            )}
                          </div>
                        </div>
                        
                        {/* 权限图标 */}
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-500">权限:</span>
                            {getPermissionIcons(auth.permissions)}
                          </div>
                        </div>
                        
                        {/* 时间信息 */}
                        <div className="grid grid-cols-2 gap-4 text-xs">
                          <div>
                            <div className="flex items-center gap-1 text-gray-500 mb-1">
                              <Calendar className="w-3 h-3" />
                              <span>授权时间</span>
                            </div>
                            <div className="text-gray-700">{formatDate(auth.granted_at)}</div>
                            <div className="text-gray-500">{getRelativeTime(auth.granted_at)}</div>
                          </div>
                          <div>
                            <div className="flex items-center gap-1 text-gray-500 mb-1">
                              <Clock className="w-3 h-3" />
                              <span>过期时间</span>
                            </div>
                            {auth.expires_at ? (
                              <>
                                <div className="text-gray-700">{formatDate(auth.expires_at)}</div>
                                <div className="text-gray-500">{getRelativeTime(auth.expires_at)}</div>
                              </>
                            ) : (
                              <span className="text-green-600 font-medium">永久有效</span>
                            )}
                          </div>
                        </div>
                        
                        {/* 授权人信息 */}
                        <div className="flex items-center gap-2 text-xs">
                          <User className="w-3 h-3 text-gray-400" />
                          <span className="text-gray-500">授权人:</span>
                          <span className="text-gray-700">{auth.granted_by_name}</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>

        {/* 分页 */}
        {total > pageSize && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-gray-500">
              显示 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, total)} 条，共 {total} 条
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1 || loading}
              >
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => prev + 1)}
                disabled={currentPage * pageSize >= total || loading}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AuthorizationRecords;
