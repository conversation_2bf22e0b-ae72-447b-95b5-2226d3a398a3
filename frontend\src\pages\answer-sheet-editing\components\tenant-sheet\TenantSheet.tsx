import { PaperVO } from '@/components/question-card/v1.0.0/entity/paper-vo'
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils'
import { tenantPaperApi } from '@/services/tenantPapersApi'
import { Paper } from '@/types/papers'
import { useEffect, useState } from 'react'
import { TenantSheet_0_0_0 } from './v0.0.0/TenantSheet_0_0_0'

export interface Props {
  paperId: string
  schemaName: string
}

/**
 * 作者：张瀚
 * 说明：租户内的题卡编辑
 */
export function TenantSheet({ paperId, schemaName }: Props) {
  const tenantInfo = getTenantInfoFromLocalStorage()
  const [nowTenantId, setNowTenantId] = useState(tenantInfo?.tenant_id)
  if (nowTenantId !== tenantInfo?.tenant_id) {
    setNowTenantId(tenantInfo?.tenant_id)
  }
  //试卷数据
  const [paper, setPaper] = useState<Paper | undefined>(undefined)
  //答题卡版本，老版本是old，其他是版本号
  const [answerCardVersion, setAnswerCardVersion] = useState<string>(TenantSheet_0_0_0.Version)
  //查询数据
  useEffect(() => {
    if (!nowTenantId) {
      return undefined
    }
    tenantPaperApi.findById(nowTenantId, schemaName, paperId).then((res) => {
      const { data } = res
      if (data) {
        setPaper(data)
        //判断版本号
        const { paper_content } = data
        let paperVO = paper_content as PaperVO
        if (!paperVO.answerCard) {
          //老版本
          setAnswerCardVersion(TenantSheet_0_0_0.Version)
        } else {
          //新版本
          setAnswerCardVersion(paperVO.answerCard.version)
        }
      }
    })
  }, [paperId, nowTenantId, schemaName])
  if (!paper) {
    return
  }
  switch (answerCardVersion) {
    case TenantSheet_0_0_0.Version:
      return <TenantSheet_0_0_0.Editor paper={paper} nowTenantId={nowTenantId ?? ''} schemaName={schemaName}></TenantSheet_0_0_0.Editor>
  }
  return <>不支持的版本：{answerCardVersion}</>
}
// /**
//      * 作者：张瀚
//      * 说明：导航栏
//      */
// function NavigationBar(props:{paperDataStore:}) {
//     const navigate = useNavigate();
//         return <div className="flex items-center justify-between" >
//             <Button variant="outline" onClick={() => navigate(-1)}>
//                 <ArrowLeft className="h-4 w-4 mr-2" />
//                 返回
//             </Button>
//             <div className="flex items-center justify-end">
//                 {
//                     nowSchemaName !== 'public' && <Button disabled={!isReadyToPrint} variant="outline" className="mr-3" onClick={() => {
//                         if (!nowTenantId || !nowId) {
//                             return
//                         }
//                         const newPaperData = getPaperData()
//                         const qrcodeBean = new AdmissionTicketQRCodeMsgBean(newPaperData.paper_content.answer_card.admissionTicketNumberInfoQuestionItemConfig.qrcodeMsg)
//                         tenantPaperApi.updatePaperId(nowTenantId, schema_name, {
//                             old_paper_id: nowPaperId,
//                             new_paper_id: qrcodeBean.paperId ?? ''
//                         }).then((res) => {
//                             const { success, message } = res
//                             if (!success) {
//                                 toast("保存失败", {
//                                     description: message,
//                                 })
//                                 return
//                             }
//                             toast("保存成功", {
//                                 description: "更新成功，页面跳转到新地址，请稍候。。。",
//                             })
//                             setTimeout(() => {
//                                 navigate(`/answerSheetEditing/${schema_name}/${qrcodeBean.paperId ?? ''}`, { replace: true })
//                             }, 1000);
//                         }, () => {
//                             toast("保存失败！请检查试卷ID是否符合UUID格式！")
//                         })
//                     }}>
//                         <Save className="h-4 w-4 mr-2" />
//                         替换试卷ID为二维码内试卷ID
//                     </Button>
//                 }
//                 <Button disabled={!isReadyToPrint} onClick={() => {
//                     if (!nowTenantId || !nowId) {
//                         return
//                     }
//                     const newPaperData = getPaperData()
//                     if (nowSchemaName === 'public') {
//                         publicPaperApi.updatePaperContent(nowTenantId, {
//                             paper_id: nowPaperId,
//                             paper_content: newPaperData.paper_content
//                         }).then((res) => {
//                             const { success, message } = res
//                             if (!success) {
//                                 toast("保存失败", {
//                                     description: message,
//                                 })
//                                 return
//                             }
//                             toast("保存成功", {
//                                 description: "数据已经更新到后台",
//                             })
//                         })
//                     } else {
//                         tenantPaperApi.updatePaperContent(nowTenantId, schema_name, {
//                             paper_id: nowPaperId,
//                             paper_content: newPaperData.paper_content
//                         }).then((res) => {
//                             const { success, message } = res
//                             if (!success) {
//                                 toast("保存失败", {
//                                     description: message,
//                                 })
//                                 return
//                             }
//                             toast("保存成功", {
//                                 description: "数据已经更新到后台",
//                             })
//                         })
//                     }
//                 }}>
//                     <Save className="h-4 w-4 mr-2" />
//                     保存
//                 </Button>
//             </div>
//         </div>
//     }
