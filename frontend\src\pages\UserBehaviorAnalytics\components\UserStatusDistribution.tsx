import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

interface UserStatusDistributionProps {
  statusData: Record<string, number>;
}

export const UserStatusDistribution: React.FC<UserStatusDistributionProps> = ({
  statusData
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>用户状态分布</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {Object.entries(statusData).map(([status, count]) => (
            <div key={status} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  status === 'online' ? 'bg-green-500' : 
                  status === 'idle' ? 'bg-yellow-500' :
                  status === 'away' ? 'bg-orange-500' : 'bg-gray-500'
                }`}></div>
                <span className="capitalize">
                  {status === 'online' ? '在线' : 
                   status === 'idle' ? '空闲' :
                   status === 'away' ? '离开' : 
                   status === 'offline' ? '离线' : status}
                </span>
              </div>
              <span className="font-medium">{count}</span>
            </div>
          ))}
        </div>
        {Object.keys(statusData).length === 0 && (
          <div className="text-center text-muted-foreground py-8">
            <p>暂无状态数据</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 