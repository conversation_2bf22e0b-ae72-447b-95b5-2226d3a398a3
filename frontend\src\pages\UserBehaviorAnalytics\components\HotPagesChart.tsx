import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { analyticsApi, PageActivity } from '@/services/analyticsApi';

interface PageData {
  path: string;
  views: number;
  name: string;
}

interface HotPagesChartProps {
  pagesData?: PageData[];
  timeRange?: string;
}

export const HotPagesChart: React.FC<HotPagesChartProps> = ({
  pagesData,
  timeRange = 'today'
}) => {
  const [pages, setPages] = useState<PageData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 页面路径到名称的映射
  const getPageName = (path: string): string => {
    const pathNameMap: Record<string, string> = {
      '/exam/create': '创建考试',
      '/exam/list': '考试列表',
      '/exam/manage': '考试管理',
      '/homework/list': '作业列表',
      '/homework/create': '创建作业',
      '/homework/manage': '作业管理',
      '/grading/center': '批改中心',
      '/grading/manual': '手动批改',
      '/grading/auto': '自动批改',
      '/statistics/overview': '统计总览',
      '/statistics/class': '班级统计',
      '/statistics/student': '学生统计',
      '/class/management': '班级管理',
      '/class/create': '创建班级',
      '/student/list': '学生列表',
      '/student/manage': '学生管理',
      '/teacher/list': '教师列表',
      '/teacher/manage': '教师管理',
      '/user/profile': '个人资料',
      '/user/settings': '用户设置',
      '/dashboard': '仪表盘',
      '/login': '登录页面',
      '/': '首页'
    };

    return pathNameMap[path] || path;
  };

  // 转换PageActivity到PageData
  const convertToPageData = (topPages: PageActivity[]): PageData[] => {
    return topPages.map(page => ({
      path: page.page_path,
      views: page.views,
      name: getPageName(page.page_path)
    }));
  };

  // 获取热门页面数据
  const fetchHotPages = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await analyticsApi.getBehaviorAnalytics({
        days: timeRange === 'today' ? 1 : timeRange === 'week' ? 7 : 30
      });

      if (response.data?.top_pages) {
        const pageData = convertToPageData(response.data.top_pages);
        setPages(pageData);
      } else {
        setPages([]);
      }
    } catch (err) {
      console.error('获取热门页面数据失败:', err);
      setError('获取数据失败');
      // 设置默认数据作为fallback
      setPages([
        { path: '/exam/create', views: 0, name: '创建考试' },
        { path: '/homework/list', views: 0, name: '作业列表' },
        { path: '/grading/center', views: 0, name: '批改中心' },
        { path: '/statistics/overview', views: 0, name: '统计总览' },
        { path: '/class/management', views: 0, name: '班级管理' }
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 如果没有传入外部数据，则获取API数据
    if (!pagesData) {
      fetchHotPages();
    } else {
      setPages(pagesData);
      setLoading(false);
    }
  }, [pagesData, timeRange]);

  const displayPages = pagesData || pages;

  return (
    <Card>
      <CardHeader>
        <CardTitle>热门页面访问</CardTitle>
        {!pagesData && (
          <div className="text-sm text-muted-foreground">
            {loading ? '加载中...' : error ? `错误: ${error}` : `显示最近${timeRange === 'today' ? '1天' : timeRange === 'week' ? '7天' : '30天'}的热门页面`}
          </div>
        )}
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center text-muted-foreground py-8">
            <div className="animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
            <p>正在加载数据...</p>
          </div>
        ) : (
          <>
            <div className="space-y-3">
              {displayPages.map((page, index) => (
                <div key={page.path} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ${index === 0 ? 'bg-yellow-100 text-yellow-800' :
                      index === 1 ? 'bg-gray-100 text-gray-800' :
                        index === 2 ? 'bg-orange-100 text-orange-800' :
                          'bg-blue-100 text-blue-800'
                      }`}>
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium">{page.name}</div>
                      <div className="text-sm text-muted-foreground">{page.path}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{page.views.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">访问次数</div>
                  </div>
                </div>
              ))}
            </div>
            {displayPages.length === 0 && !loading && (
              <div className="text-center text-muted-foreground py-8">
                <p>暂无页面访问数据</p>
                {error && (
                  <div className="mt-2">
                    <button
                      onClick={fetchHotPages}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      重试
                    </button>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}; 