import { ApiResponse } from '@/types';
import apiClient from './apiClient';

// 分析概览数据
export interface AnalyticsOverview {
  total_users: number;
  active_users_today: number;
  total_sessions_today: number;
  avg_session_duration_minutes: number;
  total_page_views_today: number;
  total_actions_today: number;
  current_online_users: number;
  peak_concurrent_today: number;
  user_growth_percentage: number;
  active_users_growth_percentage: number;
  page_views_growth_percentage: number;
}

// 实时指标数据
export interface RealTimeMetrics {
  current_online: number;
  active_last_5min: number;
  active_last_15min: number;
  active_last_hour: number;
  actions_last_hour: number;
  avg_response_time_ms: number;
  error_rate_percent: number;
  new_sessions_last_hour: number;
}

// 在线用户统计
export interface OnlineUserStats {
  total_online: number;
  by_status: Record<string, number>;
  by_module: Record<string, number>;
  by_device: Record<string, number>;
  by_tenant: Record<string, number>;
  peak_concurrent_today: number;
  avg_session_duration_minutes: number;
}

// 用户行为日志
export interface UserBehaviorLog {
  id: string;
  user_id?: string;
  tenant_id?: string;
  session_id: string;
  action_type: string;
  module: string;
  page_path?: string;
  element?: string;
  resource_type?: string;
  resource_id?: string;
  resource_name?: string;
  request_method?: string;
  request_url?: string;
  response_status?: number;
  response_time_ms?: number;
  ip_address?: string;
  device_type?: string;
  browser?: string;
  os?: string;
  success: boolean;
  error_message?: string;
  created_at: string;
}

// 用户行为分析数据
export interface BehaviorAnalyticsData {
  total_actions: number;
  unique_users: number;
  unique_sessions: number;
  avg_session_duration: number;
  most_active_modules: ModuleActivity[];
  hourly_distribution: HourlyActivity[];
  device_distribution: Record<string, number>;
  top_pages: PageActivity[];
}

export interface ModuleActivity {
  module: string;
  count: number;
  unique_users: number;
  avg_duration_seconds?: number;
}

export interface HourlyActivity {
  hour: number;
  actions: number;
  unique_users: number;
  avg_response_time_ms?: number;
}

export interface PageActivity {
  page_path: string;
  views: number;
  unique_users: number;
  avg_duration_seconds?: number;
}

// 用户行为趋势
export interface UserBehaviorTrend {
  date: string;
  unique_users: number;
  total_actions: number;
  avg_session_duration: number;
  login_count: number;
}

// 查询参数
export interface AnalyticsQueryParams {
  start_date?: string;
  end_date?: string;
  days?: number;
  module?: string;
  user_id?: string;
  device_type?: string;
}

export interface BehaviorLogQueryParams {
  user_id?: string;
  tenant_id?: string;
  session_id?: string;
  action_type?: string;
  module?: string;
  start_date?: string;
  end_date?: string;
  success?: boolean;
  page?: number;
  page_size?: number;
}

// API 服务类
export class AnalyticsApiService {

  // 获取分析概览数据
  async getOverview(): Promise<ApiResponse<AnalyticsOverview>> {
    return await apiClient.get('/api/v1/analytics/dashboard');
  }

  // 获取实时指标
  async getRealTimeMetrics(): Promise<ApiResponse<RealTimeMetrics>> {
    return await apiClient.get('/api/v1/analytics/real-time');
  }

  // 获取在线用户统计
  async getOnlineStats(): Promise<ApiResponse<OnlineUserStats>> {
    return await apiClient.get('/api/v1/analytics/online/stats');
  }

  // 获取在线用户数量
  async getOnlineCount(): Promise<ApiResponse<number>> {
    return await apiClient.get('/api/v1/analytics/online/count');
  }

  // 获取用户行为日志
  async getBehaviorLogs(
    params: BehaviorLogQueryParams = {}
  ): Promise<ApiResponse<UserBehaviorLog[]>> {
    const queryString = new URLSearchParams(
      Object.entries(params)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => [key, String(value)])
    ).toString();

    const url = `/api/v1/analytics/behavior/logs${queryString ? `?${queryString}` : ''}`;
    return await apiClient.get(url);
  }

  // 获取用户行为分析数据
  async getBehaviorAnalytics(
    params: AnalyticsQueryParams = {}
  ): Promise<ApiResponse<BehaviorAnalyticsData>> {
    const queryString = new URLSearchParams(
      Object.entries(params)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => [key, String(value)])
    ).toString();

    const url = `/api/v1/analytics/behavior/analytics${queryString ? `?${queryString}` : ''}`;
    return await apiClient.get(url);
  }

  // 获取用户行为趋势
  async getBehaviorTrends(
    params: AnalyticsQueryParams = {}
  ): Promise<ApiResponse<UserBehaviorTrend[]>> {
    const queryString = new URLSearchParams(
      Object.entries(params)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => [key, String(value)])
    ).toString();

    const url = `/api/v1/analytics/behavior/trends${queryString ? `?${queryString}` : ''}`;
    return await apiClient.get(url);
  }

  // 清理过期用户
  async cleanupInactiveUsers(): Promise<ApiResponse<number>> {
    return await apiClient.post('/api/v1/analytics/online/cleanup');
  }

  /**
   * 获取分析概览（新版API）
   */
  async getAnalyticsOverview(timeRange: string = 'today'): Promise<AnalyticsOverview> {
    try {
      const result: ApiResponse<AnalyticsOverview> = await apiClient.get(`/api/v1/analytics/overview?time_range=${timeRange}`);
      if (!result.data) {
        throw new Error('API返回数据为空');
      }
      return result.data;
    } catch (error: any) {
      throw new Error(`获取分析概览失败: ${error.message}`);
    }
  }

  /**
   * 获取实时指标（新版API）
   */
  async getRealTimeMetricsNew(): Promise<RealTimeMetrics> {
    try {
      const result: ApiResponse<RealTimeMetrics> = await apiClient.get('/api/v1/analytics/real-time');
      if (!result.data) {
        throw new Error('API返回数据为空');
      }
      return result.data;
    } catch (error: any) {
      throw new Error(`获取实时指标失败: ${error.message}`);
    }
  }

  /**
   * 获取在线用户统计（新版API）
   */
  async getOnlineUserStats(): Promise<OnlineUserStats> {
    try {
      const result: ApiResponse<OnlineUserStats> = await apiClient.get('/api/v1/analytics/online/stats');
      if (!result.data) {
        throw new Error('API返回数据为空');
      }
      return result.data;
    } catch (error: any) {
      throw new Error(`获取在线用户统计失败: ${error.message}`);
    }
  }

  /**
   * 获取用户行为趋势（新版API）
   */
  async getUserBehaviorTrends(timeRange: string = 'week'): Promise<UserBehaviorTrend[]> {
    try {
      const result: ApiResponse<UserBehaviorTrend[]> = await apiClient.get(`/api/v1/analytics/behavior/trends?time_range=${timeRange}`);
      if (!result.data) {
        throw new Error('API返回数据为空');
      }
      return result.data;
    } catch (error: any) {
      throw new Error(`获取行为趋势失败: ${error.message}`);
    }
  }
}

// 导出实例
export const analyticsApi = new AnalyticsApiService(); 