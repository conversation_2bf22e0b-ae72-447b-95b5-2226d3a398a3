@use '../global.module.scss' as base;

.select-trigger {
  button {
    all: unset;
  }

  @extend .unset;
  @extend .border-default;
  @extend .hover-default;
  @extend .disabled;
  line-height: 30px;
  padding: 0 8px;
  display: flex;
  justify-content: space-between;
  background-color: base.$background-color;
  cursor: pointer;
}

.select-content {
  button {
    all: unset;
  }

  @extend .border-default;
  overflow: hidden;
  background-color: base.$background-color;
  padding: 5px;
  z-index: 1000;
  min-width: 200px;

  .select-item {
    @extend .hover-default;
    cursor: pointer;
    padding: 0 5px;

    & + .select-item {
      border-top: 1px dashed base.$border-color;
    }
  }
}
