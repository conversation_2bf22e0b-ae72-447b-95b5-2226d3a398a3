import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Monitor, Smartphone, Tablet } from 'lucide-react';

interface DeviceDistributionProps {
  deviceData: Record<string, number>;
}

export const DeviceDistribution: React.FC<DeviceDistributionProps> = ({
  deviceData
}) => {
  const getDeviceInfo = (device: string) => {
    const deviceInfo: Record<string, { name: string; icon: React.ReactNode }> = {
      'desktop': { name: '桌面端', icon: <Monitor className="w-4 h-4 mr-2" /> },
      'mobile': { name: '移动端', icon: <Smartphone className="w-4 h-4 mr-2" /> },
      'tablet': { name: '平板', icon: <Tablet className="w-4 h-4 mr-2" /> }
    };
    return deviceInfo[device] || { name: device, icon: <Monitor className="w-4 h-4 mr-2" /> };
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>设备类型分布</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {Object.entries(deviceData).map(([device, count]) => {
            const { name, icon } = getDeviceInfo(device);
            return (
              <div key={device} className="flex items-center justify-between">
                <div className="flex items-center">
                  {icon}
                  <span>{name}</span>
                </div>
                <span className="font-medium">{count}</span>
              </div>
            );
          })}
        </div>
        {Object.keys(deviceData).length === 0 && (
          <div className="text-center text-muted-foreground py-8">
            <p>暂无设备数据</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 