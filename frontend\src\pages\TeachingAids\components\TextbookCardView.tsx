import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { TeachingAid } from '@/services/teachingAidsApi';
import { BookOpen, CalendarDays, Edit, Tag, Trash2, UserRound, Shield, FileText } from 'lucide-react';
import React from 'react';

const EmptyState: React.FC = () => (
  <div className="col-span-full flex flex-col items-center justify-center py-16 text-center border rounded-lg bg-muted/20">
    <BookOpen className="h-10 w-10 text-muted-foreground mb-3" />
    <div className="text-sm text-muted-foreground">暂无教辅资源，试试更换筛选条件或新建一个吧</div>
  </div>
);

const stringToHsl = (str: string, s: number = 65, l: number = 55) => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  const h = Math.abs(hash) % 360;
  return `hsl(${h}, ${s}%, ${l}%)`;
};

const createCoverDataUrl = (title: string) => {
  const bg1 = stringToHsl(title, 60, 55);
  const bg2 = stringToHsl(title + 'x', 70, 45);
  const clean = (title || '').trim() || '书名';

  // 两行排版规则：不超过 8 个字则一行；超过 8 个字则第一行 8 个，第二行放剩余（最多 14 个，超出省略）
  const toChars = (s: string) => Array.from(s);
  const chars = toChars(clean);
  const firstMax = 8;
  const secondMax = 14;
  let lines: string[];
  if (chars.length <= firstMax) {
    lines = [clean];
  } else {
    const first = chars.slice(0, firstMax).join('');
    let rest = chars.slice(firstMax);
    if (rest.length > secondMax) rest = rest.slice(0, secondMax).concat(['…']);
    lines = [first, rest.join('')];
  }

  const width = 600;   // 封面宽
  const height = 900;  // 封面高（2:3）

  // 根据最长行长度自适配左右边距、字号、行距
  const longest = Math.max(...lines.map(l => toChars(l).length));
  const sizing = (() => {
    if (longest <= 6) return { paddingX: 96, fontSize: 80, lineGap: 108 };
    if (longest <= 8) return { paddingX: 88, fontSize: 72, lineGap: 100 };
    if (longest <= 10) return { paddingX: 84, fontSize: 66, lineGap: 96 };
    if (longest <= 12) return { paddingX: 80, fontSize: 60, lineGap: 92 };
    return { paddingX: 76, fontSize: 56, lineGap: 88 };
  })();

  const centerX = sizing.paddingX + (width - sizing.paddingX * 2) / 2; // 在内容区内水平居中
  const baseY = lines.length === 1 ? height / 2 : (height / 2 - sizing.lineGap / 2);
  const tspans = lines
    .map((line, idx) => `<tspan x="${centerX}" y="${baseY + idx * sizing.lineGap}">${line}</tspan>`)
    .join('');

  const svg = `<?xml version="1.0" encoding="UTF-8"?>
  <svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
    <defs>
      <linearGradient id="g" x1="0" y1="0" x2="1" y2="1">
        <stop offset="0%" stop-color="${bg1}"/>
        <stop offset="100%" stop-color="${bg2}"/>
      </linearGradient>
    </defs>
    <rect width="${width}" height="${height}" fill="url(#g)"/>
    <text dominant-baseline="middle" text-anchor="middle" font-family="-apple-system,Segoe UI,Roboto,Helvetica,Arial,sans-serif" font-size="${sizing.fontSize}" font-weight="700" fill="rgba(255,255,255,0.92)">${tspans}</text>
  </svg>`;
  return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
};

const resolveCoverUrl = (aid: TeachingAid) => {
  if (aid.coverPath) {
    return aid.coverPath;
  }
  const anyAid = aid as unknown as { cover_url?: string; coverUrl?: string };
  return anyAid.cover_url || anyAid.coverUrl || createCoverDataUrl(aid.title);
};

interface TeachingAidCardViewProps {
  aids: TeachingAid[];
  onPreview: (aid: TeachingAid) => void;
  onEdit: (aid: TeachingAid) => void;
  onDelete: (id: string) => void;
  onAuthorize?: (aid: TeachingAid) => void;
  onViewAuthorizationRecords?: (aid: TeachingAid) => void;
  renderStatusBadge: (status: string) => React.ReactNode;
  renderContentTypeBadge: (contentType: string) => React.ReactNode;
}

const TextbookCardView: React.FC<TeachingAidCardViewProps> = ({
  aids,
  onPreview,
  onEdit,
  onDelete,
  onAuthorize,
  onViewAuthorizationRecords,
  renderStatusBadge,
  renderContentTypeBadge
}) => {
  const formatDate = (value: string) => new Date(value).toLocaleDateString('zh-CN');

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-5">
      {aids.length === 0 && <EmptyState />}
      {aids.map((aid) => (
        <Card key={aid.id} className="group hover:shadow-lg transition-shadow border-muted-foreground/10 cursor-pointer" onClick={() => onPreview(aid)}>
          <div className="px-4 pt-4">
            <div className="relative overflow-hidden rounded-md border bg-muted/30 aspect-[2/3]">
              <img
                src={resolveCoverUrl(aid)}
                alt={aid.title}
                className="h-full w-full object-cover"
                loading="lazy"
                onError={(e) => {
                  (e.currentTarget as HTMLImageElement).src = createCoverDataUrl(aid.title);
                }}
              />
            </div>
          </div>
          <CardHeader className="pb-2 pt-3">
            <div className="flex items-start justify-between gap-3">
              <div className="min-w-0 flex-1">
                <CardTitle className="text-base break-words">{aid.title}</CardTitle>
                {aid.description && (
                  <CardDescription className="mt-1 line-clamp-2">
                    {aid.description}
                  </CardDescription>
                )}
                <div className="mt-2 flex flex-wrap items-center gap-2">
                  {renderContentTypeBadge(aid.content_type)}
                  {renderStatusBadge(aid.status)}
                  <Badge variant="outline" className="text-xs">{aid.resource_version}</Badge>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-2 text-muted-foreground">
                <UserRound className="h-4 w-4" />
                <span>作者</span>
                <span className="font-medium text-foreground truncate">{aid.author || '-'}</span>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center gap-2 text-muted-foreground min-w-0">
                  <Tag className="h-4 w-4" />
                  <span>学科</span>
                  <span className="font-medium text-foreground truncate">{aid.subject || '-'}</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground min-w-0">
                  <BookOpen className="h-4 w-4" />
                  <span>年级</span>
                  <span className="font-medium text-foreground truncate">{aid.grade_level || '-'}</span>
                </div>
              </div>
              <div className="text-xs text-muted-foreground border-t pt-2 flex items-center gap-2 justify-between">
                <div className="flex items-center gap-2">
                  <CalendarDays className="h-3.5 w-3.5" />
                  <span>创建于 {formatDate(aid.created_at)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="sm" onClick={(event) => {
                            event.stopPropagation();
                            onEdit(aid);
                          }
                        }>
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>编辑教辅</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  {onAuthorize && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="sm" onClick={(event) => {
                              event.stopPropagation();
                              onAuthorize(aid);
                            }
                          }>
                            <Shield className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>授权给教育机构</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  {onViewAuthorizationRecords && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="sm" onClick={(event) => {
                              event.stopPropagation();
                              onViewAuthorizationRecords(aid);
                            }
                          }>
                            <FileText className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>查看授权记录</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="sm" onClick={(event) => {
                            event.stopPropagation();
                            onDelete(aid.id);
                          }
                        }>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>删除教辅</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default TextbookCardView;