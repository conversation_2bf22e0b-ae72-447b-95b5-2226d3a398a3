import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CalendarClock, Search, ChevronRight } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import CustomPagination from '@/components/Pagination'
import { HomeworkStatusEnum } from '@/types/homework'
import { Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { HomeworkFeedbackStatus } from '@/services/studentScoreApi'
import { useHomeworkStore } from '@/stores'
import { useNavigate } from 'react-router-dom'

const mockFeedbackList = [
  {
    id: 1,
    name: '张三',
    studentNo: '20230001',
    className: '三年二班',
    text: '这是一条反馈内容',
    status: 'Initial',
    files: [
      {
        id: 1,
        name: '图片1',
        url: 'http://***********:900/deep-mate/tenants/tenant_gzitv/grading/paper_scans/901e3a1f-cbe0-4c57-9ca7-336860abb65c/20250915190236031757934250/d7fbea60-a7c5-4012-8126-8e1952faf695/6a82bcff-f5d1-46b9-8e6e-fbe0c266ff05.jpg?X-Amz-SignedHeaders=host&X-Amz-Signature=0799b078ea390c631a0eec65f6c37fca97526f79fce66c95f6170b782dc25232&X-Amz-Date=20250925T095449Z&X-Amz-Credential=minio_admin%2F20250925%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Expires=604800',
      },
    ],
  },
  {
    id: 2,
    name: '张三',
    studentNo: '20230001',
    className: '三年二班',
    text: '这是一条反馈内容',
    status: 'Initial',
    files: [
      {
        id: 1,
        name: '图片1',
        url: 'http://***********:900/deep-mate/tenants/tenant_gzitv/grading/paper_scans/901e3a1f-cbe0-4c57-9ca7-336860abb65c/20250915190236031757934250/d7fbea60-a7c5-4012-8126-8e1952faf695/6a82bcff-f5d1-46b9-8e6e-fbe0c266ff05.jpg?X-Amz-SignedHeaders=host&X-Amz-Signature=0799b078ea390c631a0eec65f6c37fca97526f79fce66c95f6170b782dc25232&X-Amz-Date=20250925T095449Z&X-Amz-Credential=minio_admin%2F20250925%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Expires=604800',
      },
    ],
  },
]

const FeedbackList: React.FC = () => {
  const [error, setError] = useState<string | null>(null)
  const [searchParams, setSearchParams] = useState({
    name: '',
  })
  const navigate = useNavigate()
  const { homework } = useHomeworkStore()
  const [FeedbackList, setFeedbackList] = useState(mockFeedbackList)
  const [statusFilter, setStatusFilter] = useState<HomeworkFeedbackStatus | 'all'>('all')
  const [page, setPage] = useState(1)
  const [size, setSize] = useState(20)
  const [total, setTotal] = useState(10)

  useEffect(() => {}, [])

  const paginationChange = async (page: number, pageSize: number) => {
    await new Promise((resolve) => {
      setPage(page)
      setSize(pageSize)
      setTimeout(resolve, 0)
    })
    // fetchHomeworks();
  }

  return (
    <div>
      {/* 面包屑导航 */}
      <nav className='flex items-center space-x-1 text-sm text-muted-foreground mb-4'>
        <Button variant='ghost' size='sm' className='p-0 h-auto font-normal text-muted-foreground hover:text-foreground' onClick={() => navigate('/homework-management')}>
          作业管理
        </Button>
        <ChevronRight className='h-4 w-4' />
        <Button variant='ghost' size='sm' className='p-0 h-auto font-normal text-muted-foreground hover:text-foreground' onClick={() => navigate(`/homework-setting/${homework.id}`)}>
          {homework?.homework_name}
        </Button>
        <ChevronRight className='h-4 w-4' />
        <span className='text-foreground font-medium'>学生反馈</span>
      </nav>
      <Card className='flex flex-col flex-grow'>
        <CardHeader className='flex flex-row items-center justify-between'>
          {error && <div className='mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded'>{error}</div>}

          <h2 className='text-2xl font-bold'>学生反馈</h2>
          <div className='flex gap-4 items-center'>
            <div className='relative flex-1 w-[250px]'>
              <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
              <Input placeholder='请输入反馈关键字进行搜索' value={searchParams.name} onChange={(e) => setSearchParams({ ...searchParams, name: e.target.value })} className='pl-10' />
            </div>
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as HomeworkFeedbackStatus | 'all')}>
              <SelectTrigger className='w-[160px]'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>全部状态</SelectItem>
                <SelectItem value='draft'>已提交</SelectItem>
                <SelectItem value='Doing'>已接收</SelectItem>
                <SelectItem value='Done'>已打回</SelectItem>
                <SelectItem value='Done'>已关闭</SelectItem>
                <SelectItem value='Done'>重新打开</SelectItem>
              </SelectContent>
            </Select>
            <Button>
              <Search className='h-4 w-4 mr-2' />
              查询
            </Button>
          </div>
        </CardHeader>
        <CardContent className='flex-grow min-h-[200px]'>
          <div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-[100px]'>学生姓名</TableHead>
                  <TableHead className='w-[100px]'>学号</TableHead>
                  <TableHead className='w-[120px]'>班级</TableHead>
                  <TableHead>反馈内容</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className='w-[400px]'>原图</TableHead>
                  <TableHead>意见</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {FeedbackList.map((item, Index) => (
                  <TableRow key={Index}>
                    <TableCell className='font-medium'>{item.name}</TableCell>
                    <TableCell>{item.studentNo}</TableCell>
                    <TableCell>{item.className}</TableCell>
                    <TableCell>{item.text}</TableCell>
                    <TableCell>{item.status}</TableCell>
                    <TableCell>
                      {item.files?.map((file) => (
                        <img key={file.id} src={file.url} alt={file.name} className='w-full' />
                      ))}
                    </TableCell>
                    <TableCell>
                      <Button variant='link' className='p-0 text-blue-500'>
                        回复
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <div className='flex justify-center'>
            <CustomPagination
              className='pt-2'
              total={total}
              current={page}
              pageSize={size}
              onChange={(page, size) => {
                paginationChange(page, size)
              }}
              showSizeChanger={true}
              showTotal={true}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default FeedbackList
