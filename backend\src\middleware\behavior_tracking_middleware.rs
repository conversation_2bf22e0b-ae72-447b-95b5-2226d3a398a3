use axum::{
    extract::{Request, State},
    middleware::Next,
    response::Response,
};
use chrono::Utc;
use uuid::Uuid;
use std::sync::Arc;
use tokio::time::Instant;
use tower_http::request_id::{RequestId, MakeRequestUuid};
use axum::http::HeaderMap;
use std::net::SocketAddr;

use crate::model::analytics::user_behavior_model::CreateBehaviorLogRequest;
use crate::repository::analytics::user_behavior_repository::UserBehaviorRepository;
use crate::repository::analytics::online_users_repository::OnlineUsersRepository;
use crate::model::analytics::online_users_model::UpsertOnlineUserRequest;
use crate::utils::device_detector::DeviceInfo;
use crate::web_server::AppState;

/// 用户行为追踪中间件
pub async fn behavior_tracking_middleware(
    State(app_state): State<AppState>,
    request: Request,
    next: Next,
) -> Response {
    let start_time = Instant::now();
    let method = request.method().clone();
    let uri = request.uri().clone();
    let headers = request.headers().clone();
    
    // 检查是否需要记录这个请求
    if !should_track_request(&method, &uri.path()) {
        return next.run(request).await;
    }
    
    // 提取用户信息
    let user_id = extract_user_id_from_request(&request);
    let tenant_id = extract_tenant_id_from_request(&request);
    let session_id = extract_session_id_from_request(&request);

    // 提取设备和网络信息
    let device_info = extract_device_info(&headers);
    let ip_address = extract_ip_address(&request);
    
    // 确定模块和页面路径
    let (module, page_path) = extract_module_and_page(&uri.path());
    
    // 执行请求
    let response = next.run(request).await;
    let end_time = Instant::now();
    let response_time_ms = end_time.duration_since(start_time).as_millis() as i32;
    let status_code = response.status().as_u16();
    let success = status_code < 400;

    // 确定行为类型
    let action_type = determine_action_type(&method, &uri.path(), status_code);
    
    // 创建行为日志记录请求
    if let Some(session_id) = session_id {
        let behavior_log_request = CreateBehaviorLogRequest {
            user_id,
            tenant_id,
            session_id,
            action_type: action_type.clone(),
            module: module.clone(),
            page_path: Some(page_path.clone()),
            element: None,
            resource_type: extract_resource_type(&uri.path()),
            resource_id: extract_resource_id(&uri.path()),
            resource_name: None,
            request_method: Some(method.to_string()),
            request_url: Some(uri.to_string()),
            request_params: extract_query_params(&uri),
            response_status: Some(status_code as i32),
            response_time_ms: Some(response_time_ms),
            ip_address: ip_address.as_ref().map(|ip| ip.to_string()),
            user_agent: device_info.user_agent.clone(),
            device_type: Some(device_info.device_type.clone()),
            browser: Some(device_info.browser.clone()),
            os: Some(device_info.os.clone()),
            screen_resolution: None,
            duration_ms: None,
            success: Some(success),
            error_message: if !success { 
                Some(format!("HTTP {}", status_code)) 
            } else { 
                None 
            },
            additional_data: None,
        };

        // 异步记录行为日志
        let behavior_service_clone = app_state.user_behavior_service.clone();
        tokio::spawn(async move {
            if let Err(e) = behavior_service_clone.log_behavior(behavior_log_request).await {
                tracing::error!("Failed to log user behavior: {}", e);
            }
        });

        // 更新在线用户状态（仅对重要请求）
        if let Some(user_id) = user_id {
            let online_user_request = UpsertOnlineUserRequest {
                user_id,
                tenant_id,
                session_id,
                status: Some("online".to_string()),
                current_page: Some(page_path),
                current_module: Some(module),
                ip_address: ip_address.as_ref().map(|ip| ip.to_string()),
                user_agent: device_info.user_agent,
                device_type: Some(device_info.device_type),
                browser: Some(device_info.browser),
                os: Some(device_info.os),
            };

            let online_service_clone = app_state.online_users_service.clone();
            tokio::spawn(async move {
                if let Err(e) = online_service_clone.update_online_status(online_user_request).await {
                    tracing::error!("Failed to update online user status: {}", e);
                }
            });
        }
    }

    response
}

/// 判断是否需要追踪该请求
fn should_track_request(method: &axum::http::Method, path: &str) -> bool {
    // 排除静态资源请求
    if is_static_resource(path) {
        return false;
    }
    
    // 排除健康检查和系统监控请求
    if is_system_request(path) {
        return false;
    }
    
    // 排除高频率的轮询API
    if is_polling_api(path) {
        return false;
    }
    
    // 仅记录GET页面访问和重要的API操作
    match method {
        &axum::http::Method::GET => {
            // 记录页面访问和重要的查询API
            path.starts_with("/api/v1/") || is_page_route(path)
        },
        &axum::http::Method::POST | &axum::http::Method::PUT | 
        &axum::http::Method::PATCH | &axum::http::Method::DELETE => {
            // 记录所有写操作
            true
        },
        _ => false,
    }
}

/// 判断是否为静态资源
fn is_static_resource(path: &str) -> bool {
    let static_extensions = [
        ".css", ".js", ".map", ".png", ".jpg", ".jpeg", ".gif", ".svg", 
        ".ico", ".woff", ".woff2", ".ttf", ".eot", ".mp4", ".webm", ".pdf"
    ];
    
    static_extensions.iter().any(|ext| path.ends_with(ext)) ||
    path.starts_with("/assets/") ||
    path.starts_with("/static/") ||
    path.starts_with("/public/")
}

/// 判断是否为系统请求
fn is_system_request(path: &str) -> bool {
    let system_paths = [
        "/health", "/healthz", "/ping", "/status", "/metrics", 
        "/favicon.ico", "/robots.txt", "/sitemap.xml"
    ];
    
    system_paths.iter().any(|sys_path| path.starts_with(sys_path))
}

/// 判断是否为轮询API
fn is_polling_api(path: &str) -> bool {
    let polling_patterns = [
        "/api/v1/heartbeat", 
        "/api/v1/ping",
        "/api/v1/status",
        "/api/v1/online",
        "/api/v1/notifications/unread", // 假设有未读通知轮询
        "/api/v1/analytics/real-time",  // 🎯 添加实时分析接口
        "/api/v1/analytics/online",     // 在线用户相关轮询
    ];
    
    polling_patterns.iter().any(|pattern| path.starts_with(pattern))
}

/// 判断是否为页面路由
fn is_page_route(path: &str) -> bool {
    // 判断是否为前端页面路由（非API路径）
    !path.starts_with("/api/") && (
        path == "/" || 
        path.starts_with("/dashboard") ||
        path.starts_with("/exam") ||
        path.starts_with("/class") ||
        path.starts_with("/grading") ||
        path.starts_with("/analysis") ||
        path.starts_with("/user") ||
        path.starts_with("/tenant")
    )
}

/// 从请求头中提取用户ID
fn extract_user_id_from_request(request: &Request) -> Option<Uuid> {
    // 从JWT或认证上下文中提取用户ID
    request.extensions()
        .get::<crate::middleware::auth_middleware::AuthContext>()
        .map(|ctx| ctx.user_id)
}

/// 从请求中提取租户ID
fn extract_tenant_id_from_request(request: &Request) -> Option<Uuid> {
    // 从认证上下文中提取租户ID（取第一个租户链接）
    request.extensions()
        .get::<crate::middleware::auth_middleware::AuthContext>()
        .and_then(|ctx| ctx.tenant_links.first().map(|link| link.tenant_id))
}

/// 从请求中提取会话ID
fn extract_session_id_from_request(request: &Request) -> Option<Uuid> {
    // 生成会话ID（暂时使用用户ID作为会话标识，实际应该从JWT或session管理中获取）
    request.extensions()
        .get::<crate::middleware::auth_middleware::AuthContext>()
        .map(|ctx| ctx.user_id)
        .or_else(|| Some(Uuid::new_v4()))
}

/// 从请求头中提取设备信息
fn extract_device_info(headers: &HeaderMap) -> DeviceInfo {
    let user_agent = headers.get("user-agent")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("Unknown")
        .to_string();
    
    DeviceInfo::from_user_agent(&user_agent)
}

/// 从请求中提取IP地址
fn extract_ip_address(request: &Request) -> Option<std::net::IpAddr> {
    request.extensions()
        .get::<axum::extract::ConnectInfo<SocketAddr>>()
        .map(|connect_info| connect_info.ip())
}

/// 从URL路径中提取模块和页面路径
fn extract_module_and_page(path: &str) -> (String, String) {
    let path_segments: Vec<&str> = path.trim_start_matches('/').split('/').collect();
    
    if path_segments.len() >= 3 && path_segments[0] == "api" && path_segments[1] == "v1" {
        // API路径格式: /api/v1/{module}/{action}
        let module = path_segments.get(2).unwrap_or(&"unknown").to_string();
        (module, path.to_string())
    } else if path_segments.len() >= 1 {
        // 前端路由路径
        let module = path_segments[0].to_string();
        (module, path.to_string())
    } else {
        ("unknown".to_string(), "/".to_string())
    }
}

/// 确定行为类型
fn determine_action_type(method: &axum::http::Method, path: &str, status_code: u16) -> String {
    if status_code >= 400 {
        return "error".to_string();
    }

    match method {
        &axum::http::Method::GET => {
            if path.contains("/api/") {
                "api_call".to_string()
            } else {
                "page_view".to_string()
            }
        },
        &axum::http::Method::POST => {
            if path.contains("/login") {
                "login".to_string()
            } else if path.contains("/logout") {
                "logout".to_string()
            } else if path.contains("/upload") {
                "upload".to_string()
            } else {
                "create".to_string()
            }
        },
        &axum::http::Method::PUT | &axum::http::Method::PATCH => "update".to_string(),
        &axum::http::Method::DELETE => "delete".to_string(),
        _ => "other".to_string(),
    }
}

/// 从URL路径中提取资源类型
fn extract_resource_type(path: &str) -> Option<String> {
    let path_segments: Vec<&str> = path.trim_start_matches('/').split('/').collect();
    
    if path_segments.len() >= 3 && path_segments[0] == "api" && path_segments[1] == "v1" {
        path_segments.get(2).map(|s| s.to_string())
    } else {
        None
    }
}

/// 从URL路径中提取资源ID
fn extract_resource_id(path: &str) -> Option<Uuid> {
    let path_segments: Vec<&str> = path.trim_start_matches('/').split('/').collect();
    
    // 查找UUID格式的路径段
    for segment in path_segments.iter() {
        if let Ok(uuid) = Uuid::parse_str(segment) {
            return Some(uuid);
        }
    }
    
    None
}

/// 从URI中提取查询参数
fn extract_query_params(uri: &axum::http::Uri) -> Option<serde_json::Value> {
    uri.query().and_then(|query| {
        let params: std::collections::HashMap<String, String> = 
            url::form_urlencoded::parse(query.as_bytes())
                .into_owned()
                .collect();
        
        if params.is_empty() {
            None
        } else {
            serde_json::to_value(params).ok()
        }
    })
} 