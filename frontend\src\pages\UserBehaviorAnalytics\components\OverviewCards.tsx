import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Activity, Eye, MousePointer } from 'lucide-react';
import type { AnalyticsOverview } from '@/services/analyticsApi';

interface OverviewCardsProps {
  overview: AnalyticsOverview;
  timeRange: string;
  formatNumber: (num: number) => string;
  getTimeRangeLabel: (range: string) => string;
}

export const OverviewCards: React.FC<OverviewCardsProps> = ({
  overview,
  timeRange,
  formatNumber,
  getTimeRangeLabel
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总用户数</CardTitle>
          <Users className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(overview.total_users)}</div>
          <p className="text-xs text-muted-foreground">
            较{getTimeRangeLabel(timeRange)}增长 {(overview.user_growth_percentage || 0).toFixed(1)}%
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">今日活跃用户</CardTitle>
          <Activity className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(overview.active_users_today)}</div>
          <p className="text-xs text-muted-foreground">
            较{getTimeRangeLabel(timeRange)}增长 {(overview.active_users_growth_percentage || 0).toFixed(1)}%
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">当前在线用户</CardTitle>
          <Eye className="h-4 w-4 text-orange-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(overview.current_online_users)}</div>
          <p className="text-xs text-muted-foreground">
            峰值: {formatNumber(overview.peak_concurrent_today)}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">今日页面浏览</CardTitle>
          <MousePointer className="h-4 w-4 text-purple-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(overview.total_page_views_today)}</div>
          <p className="text-xs text-muted-foreground">
            较{getTimeRangeLabel(timeRange)}增长 {(overview.page_views_growth_percentage || 0).toFixed(1)}%
          </p>
        </CardContent>
      </Card>
    </div>
  );
}; 