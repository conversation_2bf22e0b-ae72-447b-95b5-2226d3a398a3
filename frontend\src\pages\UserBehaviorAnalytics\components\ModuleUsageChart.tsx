import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';

interface ModuleUsageChartProps {
  moduleData: Record<string, number>;
  totalUsers: number;
}

export const ModuleUsageChart: React.FC<ModuleUsageChartProps> = ({
  moduleData,
  totalUsers
}) => {
  const getModuleName = (module: string) => {
    const moduleNames: Record<string, string> = {
      'exam': '考试管理',
      'homework': '作业批改',
      'grading': '成绩统计',
      'class': '班级管理',
      'analytics': '数据分析',
      'user': '用户管理'
    };
    return moduleNames[module] || module;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>功能模块使用</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {Object.entries(moduleData).map(([module, count]) => {
            const percentage = totalUsers > 0 ? (count / totalUsers) * 100 : 0;
            return (
              <div key={module} className="flex items-center justify-between">
                <span className="capitalize">
                  {getModuleName(module)}
                </span>
                <div className="flex items-center">
                  <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                      style={{width: `${percentage}%`}}
                    ></div>
                  </div>
                  <span className="font-medium text-sm w-8 text-right">{count}</span>
                </div>
              </div>
            );
          })}
        </div>
        {Object.keys(moduleData).length === 0 && (
          <div className="text-center text-muted-foreground py-8">
            <p>暂无模块使用数据</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 