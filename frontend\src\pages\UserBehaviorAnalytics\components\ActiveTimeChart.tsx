import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { analyticsApi, HourlyActivity } from '@/services/analyticsApi';

interface TimeSlot {
  hour: string;
  users: number;
  percentage: number;
}

interface ActiveTimeChartProps {
  timeData?: TimeSlot[];
  timeRange?: string;
}

export const ActiveTimeChart: React.FC<ActiveTimeChartProps> = ({
  timeData,
  timeRange = 'today'
}) => {
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 将小时数字转换为时间段字符串
  const formatHourRange = (hour: number): string => {
    const startHour = hour.toString().padStart(2, '0');
    const endHour = (hour + 1).toString().padStart(2, '0');
    return `${startHour}:00-${endHour}:00`;
  };

  // 转换HourlyActivity到TimeSlot数据
  const convertToTimeSlots = (hourlyData: HourlyActivity[]): TimeSlot[] => {
    if (hourlyData.length === 0) return [];

    // 找到最大用户数，用于计算百分比
    const maxUsers = Math.max(...hourlyData.map(h => h.unique_users));

    return hourlyData
      .map(hour => ({
        hour: formatHourRange(hour.hour),
        users: hour.unique_users,
        percentage: maxUsers > 0 ? Math.round((hour.unique_users / maxUsers) * 100) : 0
      }))
      .sort((a, b) => {
        // 按时间排序
        const hourA = parseInt(a.hour.split(':')[0]);
        const hourB = parseInt(b.hour.split(':')[0]);
        return hourA - hourB;
      });
  };

  // 获取活跃时段数据
  const fetchActiveTimeData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await analyticsApi.getBehaviorAnalytics({
        days: timeRange === 'today' ? 1 : timeRange === 'week' ? 7 : 30
      });

      if (response.data?.hourly_distribution) {
        const timeSlotData = convertToTimeSlots(response.data.hourly_distribution);
        setTimeSlots(timeSlotData);
      } else {
        setTimeSlots([]);
      }
    } catch (err) {
      console.error('获取活跃时段数据失败:', err);
      setError('获取数据失败');
      setTimeSlots([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 如果没有传入外部数据，则获取API数据
    if (!timeData) {
      fetchActiveTimeData();
    } else {
      setTimeSlots(timeData);
      setLoading(false);
    }
  }, [timeData, timeRange]);

  const displayTimeSlots = timeData || timeSlots;

  return (
    <Card>
      <CardHeader>
        <CardTitle>用户活跃时段</CardTitle>
        {!timeData && (
          <div className="text-sm text-muted-foreground">
            {loading ? '加载中...' : error ? `错误: ${error}` : `显示最近${timeRange === 'today' ? '1天' : timeRange === 'week' ? '7天' : '30天'}的活跃时段`}
          </div>
        )}
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center text-muted-foreground py-8">
            <div className="animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
            <p>正在加载数据...</p>
          </div>
        ) : (
          <>
            <div className="space-y-4">
              {displayTimeSlots.map((slot) => (
                <div key={slot.hour} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">{slot.hour}</span>
                    <span className="text-muted-foreground">{slot.users.toLocaleString()} 用户</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-full rounded-full transition-all duration-500 ease-out"
                      style={{ width: `${slot.percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-right text-xs text-muted-foreground">
                    {slot.percentage}% 活跃度
                  </div>
                </div>
              ))}
            </div>
            {displayTimeSlots.length === 0 && !loading && (
              <div className="text-center text-muted-foreground py-8">
                <p>暂无时段活跃数据</p>
                {error && (
                  <div className="mt-2">
                    <button
                      onClick={fetchActiveTimeData}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      重试
                    </button>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}; 