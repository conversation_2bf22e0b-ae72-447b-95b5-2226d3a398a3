use sqlx::{PgPool, Row};
use anyhow::Result;
use chrono::{DateTime, Utc, NaiveDate};
use uuid::Uuid;
use std::collections::HashMap;
use std::net::IpAddr;

use crate::model::analytics::user_behavior_model::*;

/// 用户行为日志数据访问层
pub struct UserBehaviorRepository {
    pool: PgPool,
}

impl UserBehaviorRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 创建用户行为日志记录
    pub async fn create_behavior_log(&self, request: CreateBehaviorLogRequest) -> Result<Uuid> {
        let id = Uuid::new_v4();
        
        let ip_addr: Option<IpAddr> = request.ip_address
            .as_deref()
            .and_then(|s| s.parse().ok());

        sqlx::query!(
            r#"
            INSERT INTO user_behavior_logs (
                id, user_id, tenant_id, session_id,
                action_type, module, page_path, element,
                resource_type, resource_id, resource_name,
                request_method, request_url, request_params,
                response_status, response_time_ms,
                ip_address, user_agent, device_type, browser, os, screen_resolution,
                duration_ms, success, error_message, additional_data
            ) VALUES (
                $1, $2, $3, $4,
                $5, $6, $7, $8,
                $9, $10, $11,
                $12, $13, $14,
                $15, $16,
                $17, $18, $19, $20, $21, $22,
                $23, $24, $25, $26
            )
            "#,
            id,
            request.user_id,
            request.tenant_id,
            request.session_id,
            request.action_type,
            request.module,
            request.page_path,
            request.element,
            request.resource_type,
            request.resource_id,
            request.resource_name,
            request.request_method,
            request.request_url,
            request.request_params,
            request.response_status,
            request.response_time_ms,
            ip_addr.map(|addr| ipnetwork::IpNetwork::from(addr)),
            request.user_agent,
            request.device_type,
            request.browser,
            request.os,
            request.screen_resolution,
            request.duration_ms,
            request.success.unwrap_or(true),
            request.error_message,
            request.additional_data
        )
        .execute(&self.pool)
        .await?;

        Ok(id)
    }

    /// 获取用户行为日志列表（分页）
    pub async fn get_behavior_logs(
        &self,
        params: &BehaviorLogQueryParams,
    ) -> Result<Vec<UserBehaviorLog>> {
        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(50);
        let offset = (page - 1) * page_size;

        let mut conditions = Vec::new();
        let mut param_count = 1;

        // 构建动态查询条件
        if params.user_id.is_some() {
            conditions.push(format!("user_id = ${}", param_count));
            param_count += 1;
        }
        if params.tenant_id.is_some() {
            conditions.push(format!("tenant_id = ${}", param_count));
            param_count += 1;
        }
        if params.session_id.is_some() {
            conditions.push(format!("session_id = ${}", param_count));
            param_count += 1;
        }
        if params.action_type.is_some() {
            conditions.push(format!("action_type = ${}", param_count));
            param_count += 1;
        }
        if params.module.is_some() {
            conditions.push(format!("module = ${}", param_count));
            param_count += 1;
        }
        if params.start_date.is_some() {
            conditions.push(format!("created_at >= ${}", param_count));
            param_count += 1;
        }
        if params.end_date.is_some() {
            conditions.push(format!("created_at <= ${}", param_count));
            param_count += 1;
        }
        if params.success.is_some() {
            conditions.push(format!("success = ${}", param_count));
            param_count += 1;
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        let query = format!(
            r#"
            SELECT id, user_id, tenant_id, session_id,
                   action_type, module, page_path, element,
                   resource_type, resource_id, resource_name,
                   request_method, request_url, request_params,
                   response_status, response_time_ms,
                   ip_address, user_agent, device_type, browser, os, screen_resolution,
                   duration_ms, success, error_message, additional_data,
                   created_at
            FROM user_behavior_logs
            {}
            ORDER BY created_at DESC
            LIMIT {} OFFSET {}
            "#,
            where_clause, page_size, offset
        );

        let rows = sqlx::query(&query).fetch_all(&self.pool).await?;

        let logs = rows
            .into_iter()
            .map(|row| UserBehaviorLog {
                id: row.get("id"),
                user_id: row.get("user_id"),
                tenant_id: row.get("tenant_id"),
                session_id: row.get("session_id"),
                action_type: row.get("action_type"),
                module: row.get("module"),
                page_path: row.get("page_path"),
                element: row.get("element"),
                resource_type: row.get("resource_type"),
                resource_id: row.get("resource_id"),
                resource_name: row.get("resource_name"),
                request_method: row.get("request_method"),
                request_url: row.get("request_url"),
                request_params: row.get("request_params"),
                response_status: row.get("response_status"),
                response_time_ms: row.get("response_time_ms"),
                ip_address: row.get("ip_address"),
                user_agent: row.get("user_agent"),
                device_type: row.get("device_type"),
                browser: row.get("browser"),
                os: row.get("os"),
                screen_resolution: row.get("screen_resolution"),
                duration_ms: row.get("duration_ms"),
                success: row.get("success"),
                error_message: row.get("error_message"),
                additional_data: row.get("additional_data"),
                created_at: row.get("created_at"),
            })
            .collect();

        Ok(logs)
    }

    /// 获取行为分析数据
    pub async fn get_behavior_analytics(
        &self,
        tenant_id: Option<Uuid>,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> Result<BehaviorAnalyticsData> {
        let tenant_condition = if let Some(tid) = tenant_id {
            format!("AND tenant_id = '{}'", tid)
        } else {
            String::new()
        };

        // 总体统计
        let total_query = format!(
            r#"
            WITH session_durations AS (
                SELECT 
                    session_id,
                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at))) as session_duration_seconds
                FROM user_behavior_logs
                WHERE created_at >= $1 AND created_at <= $2 {}
                GROUP BY session_id
                HAVING COUNT(*) > 1
            )
            SELECT 
                COUNT(*)::bigint as total_actions,
                COUNT(DISTINCT user_id)::bigint as unique_users,
                COUNT(DISTINCT session_id)::bigint as unique_sessions,
                COALESCE((SELECT AVG(session_duration_seconds) FROM session_durations), 0.0)::float8 as avg_session_duration
            FROM user_behavior_logs
            WHERE created_at >= $1 AND created_at <= $2 {}
            "#,
            tenant_condition, tenant_condition
        );

        let total_row = sqlx::query(&total_query)
            .bind(start_date)
            .bind(end_date)
            .fetch_one(&self.pool)
            .await?;

        let total_actions: i64 = total_row.get("total_actions");
        let unique_users: i64 = total_row.get("unique_users");
        let unique_sessions: i64 = total_row.get("unique_sessions");
        let avg_session_duration: f64 = total_row.get::<Option<f64>, _>("avg_session_duration").unwrap_or(0.0);

        // 获取最活跃模块
        let modules_query = format!(
            r#"
            SELECT 
                module,
                COUNT(*)::bigint as count,
                COUNT(DISTINCT user_id)::bigint as unique_users,
                AVG(duration_ms)::float8 / 1000.0 as avg_duration_seconds
            FROM user_behavior_logs
            WHERE created_at >= $1 AND created_at <= $2 {}
            GROUP BY module
            ORDER BY count DESC
            LIMIT 10
            "#,
            tenant_condition
        );

        let module_rows = sqlx::query(&modules_query)
            .bind(start_date)
            .bind(end_date)
            .fetch_all(&self.pool)
            .await?;

        let most_active_modules: Vec<ModuleActivity> = module_rows
            .into_iter()
            .map(|row| ModuleActivity {
                module: row.get("module"),
                count: row.get("count"),
                unique_users: row.get("unique_users"),
                avg_duration_seconds: row.get("avg_duration_seconds"),
            })
            .collect();

        // 获取小时分布
        let hourly_query = format!(
            r#"
            SELECT 
                EXTRACT(HOUR FROM created_at)::int as hour,
                COUNT(*)::bigint as actions,
                COUNT(DISTINCT user_id)::bigint as unique_users,
                AVG(response_time_ms)::float8 as avg_response_time_ms
            FROM user_behavior_logs
            WHERE created_at >= $1 AND created_at <= $2 {}
            GROUP BY EXTRACT(HOUR FROM created_at)
            ORDER BY hour
            "#,
            tenant_condition
        );

        let hourly_rows = sqlx::query(&hourly_query)
            .bind(start_date)
            .bind(end_date)
            .fetch_all(&self.pool)
            .await?;

        let hourly_distribution: Vec<HourlyActivity> = hourly_rows
            .into_iter()
            .map(|row| HourlyActivity {
                hour: row.get("hour"),
                actions: row.get("actions"),
                unique_users: row.get("unique_users"),
                avg_response_time_ms: row.get("avg_response_time_ms"),
            })
            .collect();

        // 获取设备分布
        let device_query = format!(
            r#"
            SELECT 
                COALESCE(device_type, 'unknown') as device_type,
                COUNT(DISTINCT user_id)::bigint as count
            FROM user_behavior_logs
            WHERE created_at >= $1 AND created_at <= $2 {}
            GROUP BY device_type
            "#,
            tenant_condition
        );

        let device_rows = sqlx::query(&device_query)
            .bind(start_date)
            .bind(end_date)
            .fetch_all(&self.pool)
            .await?;

        let mut device_distribution = HashMap::new();
        for row in device_rows {
            let device: String = row.get("device_type");
            let count: i64 = row.get("count");
            device_distribution.insert(device, count);
        }

        // 获取热门页面
        let pages_query = format!(
            r#"
            SELECT 
                COALESCE(page_path, 'unknown') as page_path,
                COUNT(*)::bigint as views,
                COUNT(DISTINCT user_id)::bigint as unique_users,
                AVG(duration_ms)::float8 / 1000.0 as avg_duration_seconds
            FROM user_behavior_logs
            WHERE created_at >= $1 AND created_at <= $2 
              AND action_type = 'page_view' {}
            GROUP BY page_path
            ORDER BY views DESC
            LIMIT 10
            "#,
            tenant_condition
        );

        let page_rows = sqlx::query(&pages_query)
            .bind(start_date)
            .bind(end_date)
            .fetch_all(&self.pool)
            .await?;

        let top_pages: Vec<PageActivity> = page_rows
            .into_iter()
            .map(|row| PageActivity {
                page_path: row.get("page_path"),
                views: row.get("views"),
                unique_users: row.get("unique_users"),
                avg_duration_seconds: row.get("avg_duration_seconds"),
            })
            .collect();

        Ok(BehaviorAnalyticsData {
            total_actions,
            unique_users,
            unique_sessions,
            avg_session_duration,
            most_active_modules,
            hourly_distribution,
            device_distribution,
            top_pages,
        })
    }

    /// 获取用户行为趋势数据
    pub async fn get_behavior_trends(
        &self,
        tenant_id: Option<Uuid>,
        days: i32,
    ) -> Result<Vec<UserBehaviorTrend>> {
        let tenant_condition = if let Some(tid) = tenant_id {
            format!("AND tenant_id = '{}'", tid)
        } else {
            String::new()
        };

        let query = format!(
            r#"
            WITH session_durations AS (
                SELECT 
                    session_id,
                    DATE(created_at) as date,
                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at))) as session_duration_seconds
                FROM user_behavior_logs
                WHERE created_at >= NOW() - INTERVAL '{} days' {}
                GROUP BY session_id, DATE(created_at)
                HAVING COUNT(*) > 1
            )
            SELECT 
                DATE(ubl.created_at) as date,
                COUNT(*)::bigint as total_actions,
                COUNT(DISTINCT ubl.user_id)::bigint as unique_users,
                COALESCE(AVG(sd.session_duration_seconds), 0.0)::float8 as avg_session_duration,
                COUNT(DISTINCT CASE WHEN ubl.action_type = 'login' THEN ubl.session_id END)::bigint as login_count
            FROM user_behavior_logs ubl
            LEFT JOIN session_durations sd ON DATE(ubl.created_at) = sd.date
            WHERE ubl.created_at >= NOW() - INTERVAL '{} days' {}
            GROUP BY DATE(ubl.created_at)
            ORDER BY date DESC
            "#,
            days, tenant_condition, days, tenant_condition
        );

        let rows = sqlx::query(&query).fetch_all(&self.pool).await?;

        let trends = rows
            .into_iter()
            .map(|row| UserBehaviorTrend {
                date: row.get("date"),
                total_actions: row.get("total_actions"),
                unique_users: row.get("unique_users"),
                avg_session_duration: row.get("avg_session_duration"),
                login_count: row.get("login_count"),
            })
            .collect();

        Ok(trends)
    }

    /// 获取实时行为指标
    pub async fn get_real_time_metrics(&self, tenant_id: Option<Uuid>) -> Result<RealTimeBehaviorMetrics> {
        let tenant_condition = if let Some(tid) = tenant_id {
            format!("AND tenant_id = '{}'", tid)
        } else {
            String::new()
        };

        // 当前在线用户数（从在线用户表获取）
        let online_query = format!(
            r#"
            SELECT COUNT(DISTINCT user_id)::bigint as current_online
            FROM online_users
            WHERE status = 'online' 
              AND last_activity_at >= NOW() - INTERVAL '5 minutes' {}
            "#,
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let current_online = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, i64>(&online_query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, i64>(&online_query)
                .fetch_one(&self.pool)
                .await?
        };

        // 最近1小时的活动
        let hour_query = format!(
            r#"
            SELECT COUNT(*)::bigint as actions_last_hour
            FROM user_behavior_logs
            WHERE created_at >= NOW() - INTERVAL '1 hour' {}
            "#,
            tenant_condition
        );

        let actions_last_hour: i64 = sqlx::query_scalar(&hour_query)
            .fetch_one(&self.pool)
            .await?;

        // 今天的活动
        let today_query = format!(
            r#"
            SELECT COUNT(*)::bigint as actions_today
            FROM user_behavior_logs
            WHERE DATE(created_at) = CURRENT_DATE {}
            "#,
            tenant_condition
        );

        let actions_today: i64 = sqlx::query_scalar(&today_query)
            .fetch_one(&self.pool)
            .await?;

        // 平均响应时间
        let response_time_query = format!(
            r#"
            SELECT AVG(response_time_ms)::float8 as avg_response_time
            FROM user_behavior_logs
            WHERE created_at >= NOW() - INTERVAL '1 hour' 
              AND response_time_ms IS NOT NULL {}
            "#,
            tenant_condition
        );

        let avg_response_time_ms: f64 = sqlx::query_scalar::<_, Option<f64>>(&response_time_query)
            .fetch_one(&self.pool)
            .await?
            .unwrap_or(0.0);

        // 错误率
        let error_rate_query = format!(
            r#"
            SELECT 
                (COUNT(CASE WHEN success = false THEN 1 END)::float8 / COUNT(*)::float8 * 100.0) as error_rate
            FROM user_behavior_logs
            WHERE created_at >= NOW() - INTERVAL '1 hour' {}
            "#,
            tenant_condition
        );

        let error_rate_percent: f64 = sqlx::query_scalar::<_, Option<f64>>(&error_rate_query)
            .fetch_one(&self.pool)
            .await?
            .unwrap_or(0.0);

        // 最活跃模块（最近1小时）
        let top_modules_query = format!(
            r#"
            SELECT 
                module,
                COUNT(*)::bigint as count,
                COUNT(DISTINCT user_id)::bigint as unique_users
            FROM user_behavior_logs
            WHERE created_at >= NOW() - INTERVAL '1 hour' {}
            GROUP BY module
            ORDER BY count DESC
            LIMIT 5
            "#,
            tenant_condition
        );

        let module_rows = sqlx::query(&top_modules_query)
            .fetch_all(&self.pool)
            .await?;

        let top_active_modules: Vec<ModuleActivity> = module_rows
            .into_iter()
            .map(|row| ModuleActivity {
                module: row.get("module"),
                count: row.get("count"),
                unique_users: row.get("unique_users"),
                avg_duration_seconds: None,
            })
            .collect();

        Ok(RealTimeBehaviorMetrics {
            current_online_users: current_online,
            actions_last_hour,
            actions_today,
            avg_response_time_ms,
            error_rate_percent,
            top_active_modules,
        })
    }
} 