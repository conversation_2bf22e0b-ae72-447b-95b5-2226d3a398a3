'use client';

import { useEffect, useRef } from 'react';

interface WindowActiveTracker {
  totalActiveMs: number;
  activeStart: number | null;
  isTracking: boolean;
  reported: boolean;
  activityTimeoutId: number | null;
  minDurationMs: number;
  onReport: ((durationMs: number) => void) | null;
}

const getOrCreateGlobalState = (sessionId: string): WindowActiveTracker => {
  if (!(window as any).__activeTracker) {
    (window as any).__activeTracker = {};
  }
  const globalStore = (window as any).__activeTracker;

  if (!globalStore[sessionId]) {
    globalStore[sessionId] = {
      totalActiveMs: 0,
      activeStart: null,
      isTracking: false,
      reported: false,
      activityTimeoutId: null,
      minDurationMs: 15000,
      onReport: null,
    };
  }

  return globalStore[sessionId];
};

interface UseActiveDurationTrackerProps {
  minDurationMs?: number;
  onReport: (durationMs: number) => void;
  sessionId: string;
}

export const useActiveDurationTracker = ({
  minDurationMs = 15000,
  onReport,
  sessionId,
}: UseActiveDurationTrackerProps) => {
  const state = getOrCreateGlobalState(sessionId);
  state.minDurationMs = minDurationMs;
  state.onReport = onReport;

  const stateRef = useRef(state);
  stateRef.current = state;

  const handleUserActivity = () => {
    const s = stateRef.current;
    if (s.reported) return;

    if (!s.isTracking) {
      s.isTracking = true;
      s.activeStart = performance.now();

      s.activityTimeoutId = window.setTimeout(() => {
        endActivePeriod(s);
      }, 2000);
    } else {
      if (s.activityTimeoutId !== null) {
        window.clearTimeout(s.activityTimeoutId);
      }
      s.activityTimeoutId = window.setTimeout(() => {
        endActivePeriod(s);
      }, 2000);
    }
  };

  const endActivePeriod = (s: WindowActiveTracker) => {
    if (s.isTracking && s.activeStart !== null) {
      console.log('endActivePeriod', s.totalActiveMs);
      const elapsed = performance.now() - s.activeStart;
      s.totalActiveMs += elapsed;
      s.isTracking = false;
      s.activeStart = null;
    }
  };

  // 页面关闭/刷新时上报
  const handleBeforeUnload = () => {
    const s = stateRef.current;
    endActivePeriod(s);
    if (s.reported) return;

    if (s.totalActiveMs >= s.minDurationMs && s.onReport) {
      s.reported = true;
      s.onReport(s.totalActiveMs);
    }
  };

  useEffect(() => {
    const s = stateRef.current;
    if (s.reported) return;

    window.addEventListener('mousemove', handleUserActivity);
    window.addEventListener('scroll', handleUserActivity, { passive: true });

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('pagehide', handleBeforeUnload);

    window.addEventListener('click', handleUserActivity);
    window.addEventListener('touchstart', handleUserActivity, { passive: true }); // 移动端

    return () => {
      console.log(`[ActiveTracker] 组件卸载，但活跃状态保留。当前时长：${s.totalActiveMs}ms`);
    };
  }, [sessionId]); 

  const getCurrentDuration = () => {
    const s = stateRef.current;
    let current = s.totalActiveMs;
    if (s.isTracking && s.activeStart) {
      current += performance.now() - s.activeStart;
    }
    return current;
  };

  return {
    currentActiveMs: getCurrentDuration(),
  };
};