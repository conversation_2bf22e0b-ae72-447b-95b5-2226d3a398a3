import QuestionCard from '@/components/question-card/v0.0.0'
import { createComponentDataListStore } from '@/components/question-card/v0.0.0/store/componentDataListStore'
import { createDataCallbackStore, Workflow } from '@/components/question-card/v0.0.0/store/dataCallbackStore'
import { createPaperDataStore, PaperContentData, PaperDataStoreProps } from '@/components/question-card/v0.0.0/store/paperDataStore'
import { createToolbarDataStore } from '@/components/question-card/v0.0.0/store/toolbarDataStore'
import { Button } from '@/components/ui/button'
import { AnswerCardVersions } from '@/lib/answer-card-version'
import { publicPaperApi } from '@/services/publicPapersApi'
import { questionTypeApi } from '@/services/questionApi'
import { workflowSettingApi } from '@/services/workflowApi'
import { QuestionType } from '@/types'
import { Paper } from '@/types/papers'
import { ArrowLeft, Save } from 'lucide-react'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'sonner'
import { StoreApi, UseBoundStore } from 'zustand'

/**
 * 作者：张瀚
 * 说明：旧版本的公共域题卡编辑（教辅）
 */
export const PublicSheet_0_0_0 = {
  Editor,
  Version: AnswerCardVersions.v0_0_0,
}

function Editor(props: { paper: Paper; nowTenantId: string }) {
  const { paper, nowTenantId } = props
  const componentDataListStore = useMemo(() => createComponentDataListStore(), [])
  const paperDataStore = useMemo(() => createPaperDataStore(), [])
  const toolbarDataStore = useMemo(() => createToolbarDataStore(), [])
  const dataCallbackStore = useMemo(() => createDataCallbackStore(), [])
  const setAllCallback = dataCallbackStore((state) => state.setAllCallback)
  const setPaperData = paperDataStore((state) => state.setPaperData)
  const rootRef = useRef<HTMLDivElement>(null)
  const [rootHeight, setRootHeight] = useState(60)
  setPaperData({
    ...paper,
    paper_content: paper.paper_content as PaperContentData,
  })

  useEffect(() => {
    //设置回调
    setAllCallback({
      getOcrWorkflowList: function (): Promise<Workflow[]> {
        return new Promise<Workflow[]>((resolve) => {
          workflowSettingApi.getWorkflowSummaryInSetting({ workflow_type: 'ocr' }).then((res) => {
            const { success, data, message } = res
            if (!success) {
              console.error('报错:' + message)
              return
            }
            resolve(data?.map((item) => ({ ...item })) ?? [])
          })
        })
      },
      getCheckWorkflowList: function (): Promise<Workflow[]> {
        return new Promise<Workflow[]>((resolve) => {
          workflowSettingApi.getWorkflowSummaryInSetting({ workflow_type: 'correction' }).then((res) => {
            const { success, data, message } = res
            if (!success) {
              console.error('报错:' + message)
              return
            }
            resolve(data?.map((item) => ({ ...item })) ?? [])
          })
        })
      },
      getQuestionTypeList: function (): Promise<QuestionType[]> {
        return new Promise<QuestionType[]>((resolve) => {
          questionTypeApi.getQuestionTypeSummaries(true).then((res) => {
            const { success, data, message } = res
            if (!success) {
              console.error('报错:' + message)
              return
            }
            resolve(
              data?.map((item) => {
                const { code, type_name } = item
                return {
                  code,
                  type_name,
                } satisfies QuestionType
              }) ?? []
            )
          })
        })
      },
    })
  }, [setAllCallback])

  useEffect(() => {
    if (rootRef.current) {
      const rootDom = rootRef.current.parentElement?.parentElement
      if (rootDom) {
        const { height } = rootDom.getBoundingClientRect()
        const computedStyle = window.getComputedStyle(rootDom)
        const paddingTop = parseFloat(computedStyle.paddingTop)
        const paddingBottom = parseFloat(computedStyle.paddingBottom)
        const targetHeight = Math.floor(height - paddingTop - paddingBottom)
        setRootHeight(targetHeight ?? 0)
      }
    }
  }, [])

  return (
    <div ref={rootRef}>
      <NavigationBar paper={paper} paperDataStore={paperDataStore} nowTenantId={nowTenantId}></NavigationBar>
      <QuestionCard
        componentDataListStore={componentDataListStore}
        toolbarDataStore={toolbarDataStore}
        paperDataStore={paperDataStore}
        dataCallbackStore={dataCallbackStore}
        className='w-full mt-2'
        style={{ height: `${rootHeight - 50}px` }}
      />
    </div>
  )
}

/**
 * 作者：张瀚
 * 说明：导航栏
 */
function NavigationBar(props: { paper: Paper; paperDataStore: UseBoundStore<StoreApi<PaperDataStoreProps>>; nowTenantId: string }) {
  const { paperDataStore, nowTenantId, paper } = props
  const isReadyToPrint = paperDataStore((state) => state.isReadyToPrint)
  const getPaperData = paperDataStore((state) => state.getPaperData)
  const navigate = useNavigate()
  return (
    <div className='flex items-center justify-between'>
      <Button variant='outline' onClick={() => navigate(-1)}>
        <ArrowLeft className='h-4 w-4 mr-2' />
        返回
      </Button>
      <div className='flex items-center justify-end'>
        <Button
          disabled={!isReadyToPrint}
          onClick={() => {
            const newPaperData = getPaperData()
            publicPaperApi
              .updatePaperContent(nowTenantId, {
                paper_id: paper.id,
                paper_content: newPaperData.paper_content,
              })
              .then((res) => {
                const { success, message } = res
                if (!success) {
                  toast('保存失败', {
                    description: message,
                  })
                  return
                }
                toast('保存成功', {
                  description: '数据已经更新到后台',
                })
              })
          }}
        >
          <Save className='h-4 w-4 mr-2' />
          保存
        </Button>
      </div>
    </div>
  )
}
