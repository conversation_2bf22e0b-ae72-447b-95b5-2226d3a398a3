use std::collections::HashMap;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::model::public_resource::catalog::Catalog;
use crate::utils::date_time;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct CatalogJson {
    pub id: String,
    pub book_id: String,
    pub parent_id: Option<String>,
    pub serial: i16,
    pub level: i16,
    pub title: String,
    pub section_id: Option<String>,
    #[serde(deserialize_with = "date_time::deserialize")]
    pub update_at: DateTime<Utc>,
    #[serde(default)]
    pub is_deleted: bool,
}

impl Catalog {
    pub fn from_v2(value: CatalogJson, catalog_map: &HashMap<i64, Uuid>, book_map: &HashMap<i64, Uuid>, section_map: &HashMap<i64, Uuid>) -> Self {
        let origin_id = value.id.parse().ok();
        let section_id = if let Some(section_id) = value.section_id {
            section_map.get(&section_id.parse::<i64>().unwrap_or_default()).cloned()
        } else {
            None
        };
        let book_id = book_map.get(&value.book_id.parse::<i64>().unwrap_or_default()).cloned().unwrap_or_default();
        let id = catalog_map.get(&origin_id.unwrap_or_default()).cloned().unwrap_or_default();
        let parent_id = if let Some(parent_id) = value.parent_id {
            catalog_map.get(&parent_id.parse::<i64>().unwrap_or_default()).cloned()
        } else {
            None
        };
        Self {
            id,
            book_id,
            parent_id,
            serial: value.serial,
            level: value.level,
            title: value.title,
            section_id,
            updated_at: Some(value.update_at),
            origin_id,
            is_deleted: value.is_deleted,
        }
    }
}