use crate::model::public_resource::book::Book;
use crate::model::public_resource::catalog::Catalog;
use crate::model::teaching_aids::textbooks::{BookQuery, BookUpdatePayload, TeachingAidChapter2, Textbook, TextbookVO};
use crate::model::textbooks::TeachingAidQuery;
use crate::model::{PageParams, PageResult};
use anyhow::anyhow;
use chrono::Utc;
use sqlx::{pool::PoolConnection, Executor, PgPool, Postgres, QueryBuilder, Transaction};
use std::collections::HashMap;
use uuid::Uuid;

pub struct TextbooksRepository;

impl TextbooksRepository {
    pub fn gen_insert_textbook_query<'a>(request: Textbook) -> QueryBuilder<'a, Postgres> {
        let mut builder = QueryBuilder::new(r#"INSERT INTO public.textbooks
         (id, title, subject_id, grade_level_id, publisher, publication_year, isbn, version, status,
         cover_path, creator_id, created_at, updated_at,
         subject_code, grade_level_code, distributor, edition, printing_version, authors, summary, resource_version) VALUES "#);
        builder.push("(").push_bind(request.id)
            .push(", ").push_bind(request.title)
            .push(", ").push_bind(request.subject_id)
            .push(", ").push_bind(request.grade_level_id)
            .push(", ").push_bind(request.publisher)
            .push(", ").push_bind(request.publication_year)
            .push(", ").push_bind(request.isbn)
            .push(", ").push_bind(request.version)
            .push(", ").push_bind(request.cover_path)
            .push(", ").push_bind(request.status)
            .push(", ").push_bind(request.creator_id)
            .push(", ").push_bind(request.created_at)
            .push(", ").push_bind(request.updated_at)
            .push(", ").push_bind(request.subject_code)
            .push(", ").push_bind(request.grade_level_code)
            .push(", ").push_bind(request.distributor)
            .push(", ").push_bind(request.edition)
            .push(", ").push_bind(request.printing_version)
            .push(", ").push_bind(request.authors)
            .push(", ").push_bind(request.summary)
            .push(", ").push_bind(request.resource_version)
            .push(") RETURNING * ");
        builder
    }
    pub async fn insert_textbook(pool: &PgPool, request: &Textbook) -> anyhow::Result<Textbook> {
        let mut builder = Self::gen_insert_textbook_query(request.clone());
        let textbook = builder.build_query_as::<Textbook>().fetch_one(pool).await?;
        Ok(textbook)
    }
    fn gen_textbook_vo<'a>() -> QueryBuilder<'a, Postgres> {
        let builder = QueryBuilder::new(r#"
            SELECT DISTINCT t.*, s.name AS subject, g.name AS grade_level
            FROM public.textbooks AS t
            LEFT JOIN public.subjects AS s ON t.subject_id = s.id
            LEFT JOIN public.grade_levels AS g ON t.grade_level_id = g.id
            "#);
        builder
    }

    fn gen_textbook_count<'a>() -> QueryBuilder<'a, Postgres> {
        let builder = QueryBuilder::new(r#"
        SELECT COUNT(DISTINCT t.id) AS total
        FROM public.textbooks AS t
        LEFT JOIN public.subjects AS s ON t.subject_id = s.id
        LEFT JOIN public.grade_levels AS g ON t.grade_level_id = g.id
    "#);
        builder
    }

    pub async fn find_textbook_by_id<'e, E>(executor: E, id: Uuid) -> anyhow::Result<TextbookVO>
    where E: 'e + Executor<'e, Database = Postgres>{
        let mut builder = Self::gen_textbook_vo();
        builder.push("WHERE t.id = ").push_bind(id);
        let textbook = builder.build_query_as().fetch_one(executor).await?;
        Ok(textbook)
    }

    pub async fn list_textbooks(pool: &PgPool,
                                params:TeachingAidQuery,) -> anyhow::Result<PageResult<TextbookVO>> {

        fn build_where_clause(
            builder: &mut QueryBuilder<Postgres>,
            params: &TeachingAidQuery,
        ) {
            let mut has_where = false;

            // 添加搜索条件
            if let Some(search) = &params.search {
                if !search.trim().is_empty() {
                    if has_where {
                        builder.push(" AND ");
                    } else {
                        builder.push(" WHERE ");
                    }
                    let pattern = format!("%{}%", search.trim());
                    builder
                        .push(" (t.title ILIKE ")
                        .push_bind(pattern.clone())
                        .push(" OR t.id::text ILIKE ")
                        .push_bind(pattern)
                        .push(")");
                    has_where = true;
                }
            }

            if let Some(code) = &params.subject_code {
                if !code.trim().is_empty(){
                    if has_where {
                        builder.push(" AND ");
                    } else {
                        builder.push(" WHERE ");
                    }
                    builder.push(" s.code = ").push_bind(code.to_string());
                    has_where = true;
                }
            }

            if let Some(code) = &params.grade_level_code {
                if !code.trim().is_empty(){
                    if has_where {
                        builder.push(" AND ");
                    } else {
                        builder.push(" WHERE ");
                    }
                    builder.push(" g.code = ").push_bind(code.to_string());
                    has_where = true;
                }
            }

            // 可选版本
            if let Some(version) = &params.resource_version {
                if !version.trim().is_empty() {
                    if has_where {
                        builder.push(" AND ");
                    } else {
                        builder.push(" WHERE ");
                    }
                    if version == "V1" {
                        // 数据库中 V1 对应 NULL
                        builder.push(" t.resource_version IS NULL ");
                    } else {
                        builder.push(" t.resource_version = ").push_bind(version.to_string());
                    }
                }
            }

        }


        let mut builder = Self::gen_textbook_vo();
        let mut count_builder = Self::gen_textbook_count();

        let pagination = PageParams {
            page: params.page,
            page_size: params.page_size,
        };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();

        build_where_clause(&mut builder, &params);
        build_where_clause(&mut count_builder, &params);

        builder.push(" LIMIT ")
            .push_bind(page_size)
            .push(" OFFSET ")
            .push_bind(offset);

        let total: i64 = count_builder
            .build_query_scalar()
            .fetch_one(pool)
            .await?;

        let textbooks = builder.build_query_as().fetch_all(pool).await?;

        Ok(PageResult {
            data: textbooks,
            total,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: (total + page_size as i64 - 1) / page_size as i64,
        })
    }

    pub async fn list_latest_textbooks(pool: &PgPool, limit: i64) -> anyhow::Result<Vec<TextbookVO>> {
        let mut builder = Self::gen_textbook_vo();
        builder.push("order by t.created_at desc LIMIT ").push_bind(limit);
        let textbooks = builder.build_query_as().fetch_all(pool).await?;
        Ok(textbooks)
    }
    pub fn gen_update_textbook_query<'a>(request: Textbook) -> QueryBuilder<'a, Postgres> {
        let mut builder = QueryBuilder::new(r#"UPDATE public.textbooks SET "#);
        builder.push(" title = ").push_bind(request.title)
            .push(", subject_id = ").push_bind(request.subject_id)
            .push(", grade_level_id = ").push_bind(request.grade_level_id)
            .push(", publisher = ").push_bind(request.publisher)
            .push(", publication_year = ").push_bind(request.publication_year)
            .push(", isbn = ").push_bind(request.isbn)
            .push(", version = ").push_bind(request.version)
            .push(", cover_path = ").push_bind(request.cover_path)
            .push(", status = ").push_bind(request.status)
            .push(", creator_id = ").push_bind(request.creator_id)
            .push(", updated_at = ").push_bind(Utc::now())
            .push(", subject_code = ").push_bind(request.subject_code)
            .push(", grade_level_code = ").push_bind(request.grade_level_code)
            .push(", distributor = ").push_bind(request.distributor)
            .push(", edition = ").push_bind(request.edition)
            .push(", printing_version = ").push_bind(request.printing_version)
            .push(", authors = ").push_bind(request.authors)
            .push(", summary = ").push_bind(request.summary)
            .push(", resource_version = ").push_bind(request.resource_version)
            .push(" WHERE id = ").push_bind(request.id).push(" RETURNING * ");
        builder
    }
    pub async fn update_textbook(pool: &PgPool, request: Textbook) -> anyhow::Result<Textbook> {
        let textbook = Self::gen_update_textbook_query(request).build_query_as().fetch_one(pool).await?;
        Ok(textbook)
    }

    pub async fn delete_textbook(pool: &PgPool, id: Uuid) -> anyhow::Result<()> {
        let result = sqlx::query!("DELETE FROM public.textbooks WHERE id = $1", id).execute(pool).await?;
        if result.rows_affected() == 0 {
            return Err(anyhow!("Textbook not found"));
        }
        Ok(())
    }

    pub async fn insert_chapter(conn: &mut PoolConnection<Postgres>, chapter: &TeachingAidChapter2, textbook_id: Uuid) -> anyhow::Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO public.chapters (id, textbook_id, parent_id, chapter_number, title, content, metadata, creator_id, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            "#,
            chapter.id,
            textbook_id,
            chapter.parent_id,
            chapter.chapter_number,
            chapter.title,
            chapter.content,
            chapter.metadata,
            chapter.creator_id,
            chapter.created_at,
            chapter.updated_at,
        )
        .execute(&mut **conn)
        .await?;
        Ok(())
    }

    pub async fn list_chapters_by_textbook_id(pool: &PgPool, textbook_id: Uuid) -> anyhow::Result<Vec<TeachingAidChapter2>> {
        let chapters = sqlx::query_as!(
            TeachingAidChapter2,
            "SELECT id, textbook_id, parent_id, chapter_number, title, description, content, metadata, creator_id, created_at, updated_at FROM public.chapters WHERE textbook_id = $1",
            textbook_id
        )
        .fetch_all(pool)
        .await?;
        Ok(chapters)
    }

    pub async fn get_chapter_content(pool: &PgPool, chapter_id: Uuid) -> anyhow::Result<Option<serde_json::Value>> {
        let result: Option<Option<serde_json::Value>> = sqlx::query_scalar!(r#"SELECT content FROM public.chapters WHERE id = $1"#, chapter_id)
            .fetch_optional(pool)
            .await?;
        Ok(result.flatten())
    }

    pub async fn list_catalogs_by_book_id(pool: &PgPool, book_id: Uuid) -> anyhow::Result<Vec<Catalog>> {
        let mut builder = QueryBuilder::new("SELECT c.* FROM public.catalogs AS c WHERE c.book_id = ");
        builder.push_bind(book_id).push(" ORDER BY c.serial ASC");
        let catalogues = builder.build_query_as().fetch_all(pool).await?;
        Ok(catalogues)
    }

    pub fn gen_query_homeworks(is_count: bool, params: &BookQuery) -> QueryBuilder<Postgres> {
        let fetch_str = if is_count {"COUNT(*)"} else { "*" };
        let mut builder = QueryBuilder::new(format!("SELECT {} FROM public.books b", fetch_str));
        let mut where_count = 0;
        if let Some(search) = &params.search {
            if !search.trim().is_empty() {
                if where_count > 0 { builder.push(" AND "); }
                if where_count == 0 { builder.push(" WHERE "); }
                let pattern = format!("%{}%", search.trim());
                builder.push(" (b.id::text ILIKE ").push_bind(pattern.clone()).push(" OR b.title ILIKE ").push_bind(pattern).push(")");
                let _ = where_count + 1;
            }
        }
        if let Some(subject_code) = &params.subject_code {
            if !subject_code.trim().is_empty() {
                if where_count > 0 { builder.push(" AND "); }
                if where_count == 0 { builder.push(" WHERE "); }
                builder.push(" b.subject_code = ").push_bind(subject_code.to_string());
                where_count += 1;
            }
        }
        if let Some(gl_code) = &params.grade_level_code {
            if !gl_code.trim().is_empty() {
                if where_count > 0 { builder.push(" AND "); }
                if where_count == 0 { builder.push(" WHERE "); }
                builder.push(" b.grade_level_code = ").push_bind(gl_code.to_string());
            }
        }
        builder
    }
    pub async fn page_books(pool: &PgPool, params: &BookQuery) -> anyhow::Result<(Vec<Book>, i64, i64, i64)> {
        let mut count_builder = Self::gen_query_homeworks(true, params);
        let mut query_builder = Self::gen_query_homeworks(false, params);
        let pagination = PageParams { page: params.page, page_size: params.page_size };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();
        query_builder.push(" LIMIT ").push_bind(page_size).push(" OFFSET ").push_bind(offset);
        let total: i64 = count_builder.build_query_scalar().fetch_one(pool).await?;
        let data = query_builder.build_query_as::<Book>().fetch_all(pool).await?;
        Ok((data, total, page as i64, page_size as i64))
    }

    pub async fn update_book(pool: &PgPool, book_id: Uuid, request: &BookUpdatePayload) -> anyhow::Result<Book> {
        let authors_vec: Vec<String> = request.authors.split('，').map(|s| s.trim().to_string()).filter(|s| !s.is_empty()).collect();
        let updated: Book = sqlx::query_as::<_, Book>(
            r#"
            UPDATE books
            SET
                title = $1,
                authors = $2,
                subject_code = $3,
                grade_level_code = $4,
                updated_at = NOW()
            WHERE id = $5
            RETURNING *
            "#,
        )
        .bind(&request.title)
        .bind(&authors_vec)
        .bind(&request.subject_code)
        .bind(&request.grade_level_code)
        .bind(book_id)
        .fetch_one(pool)
        .await?;
        Ok(updated)
    }

    /// 获取特定租户列表授权的教材
    pub async fn list_authorized_textbooks_for_tenants(
        pool: &PgPool,
        tenant_ids: Vec<Uuid>,
        params:TeachingAidQuery,
    ) -> anyhow::Result<PageResult<TextbookVO>> {
        if tenant_ids.is_empty() {
           return Ok(PageResult::empty());
        }

        fn build_filters<'a>(
            builder: &mut QueryBuilder<'a, Postgres>,
            tenant_ids: &'a [Uuid],
            params: &'a TeachingAidQuery,
        ) {

            // 固定 INNER JOIN + tenant 条件
            builder.push(
                " INNER JOIN public.textbook_tenant_authorizations AS tta ON t.id = tta.textbook_id ",
            );
            builder.push(" WHERE tta.tenant_id = ANY(")
                .push_bind(tenant_ids)
                .push(") ");

            builder.push(" AND tta.status = 'active' AND (tta.expires_at IS NULL OR tta.expires_at > NOW()) ");

            // 可选搜索条件
            if let Some(search) = &params.search {
                if !search.trim().is_empty() {
                    builder.push(" AND ");
                    let pattern = format!("%{}%", search.trim());
                    builder
                        .push(" (t.title ILIKE ")
                        .push_bind(pattern.clone())
                        .push(" OR t.id::text ILIKE ")
                        .push_bind(pattern.clone())
                        .push(") ");
                }
            }

            // 可选学科条件
            if let Some(code) = &params.subject_code {
                if !code.trim().is_empty() {
                    builder.push(" AND ");
                    builder.push(" s.code = ").push_bind(code.as_str());
                }
            }

            // 可选年级条件
            if let Some(code) = &params.grade_level_code {
                if !code.trim().is_empty() {
                    builder.push(" AND ");
                    builder.push(" g.code = ").push_bind(code.as_str());
                }
            }

            // 可选版本
            if let Some(version) = &params.resource_version {
                if !version.trim().is_empty() {
                    builder.push(" AND ");
                    if version == "V1" {
                        // 数据库中 V1 对应 NULL
                        builder.push(" t.resource_version IS NULL ");
                    } else {
                        builder.push(" t.resource_version = ").push_bind(version.to_string());
                    }
                }
            }
        }


        let mut builder = Self::gen_textbook_vo();
        let mut count_builder = Self::gen_textbook_count();

        let pagination = PageParams {
            page: params.page,
            page_size: params.page_size,
        };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();

        build_filters(&mut builder, &tenant_ids, &params);
        build_filters(&mut count_builder, &tenant_ids, &params);

        let total: i64 = count_builder
            .build_query_scalar()
            .fetch_one(pool)
            .await?;

        builder.push(" LIMIT ")
            .push_bind(page_size)
            .push(" OFFSET ")
            .push_bind(offset);

        let textbooks = builder.build_query_as().fetch_all(pool).await?;

        Ok(PageResult {
            data: textbooks,
            total,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: (total + page_size as i64 - 1) / page_size as i64,
        })
    }
    /// 获取特定租户列表授权且符合学科权限的教材
    /// subject_ids: None表示全部学科权限（管理员），Some(vec![])表示无学科权限，Some(ids)表示特定学科权限
    pub async fn list_authorized_textbooks_for_tenants_with_subjects(
        pool: &PgPool,
        tenant_ids: Vec<Uuid>,
        subject_ids: Option<Vec<Uuid>>,
        params:TeachingAidQuery,
    ) -> anyhow::Result<PageResult<TextbookVO>> {
        if tenant_ids.is_empty() {
            return Ok(PageResult::empty());
        }

        match subject_ids {
            // 管理员权限：返回所有授权教材
            None => {
                Self::list_authorized_textbooks_for_tenants(pool, tenant_ids, params).await
            }
            // 特定学科权限：按学科过滤
            Some(ids) => {
                if ids.is_empty() {
                    // 无学科权限，返回空
                    return Ok(PageResult::empty());
                }

                fn build_apply_filters<'a>(
                    builder: &mut QueryBuilder<'a, Postgres>,
                    tenant_ids: &'a [Uuid],
                    subject_ids: &'a [Uuid],
                    params: &TeachingAidQuery,
                ) {
                    builder
                        .push(" INNER JOIN public.textbook_tenant_authorizations AS tta ON t.id = tta.textbook_id ")
                        .push("WHERE tta.tenant_id = ANY(").push_bind(tenant_ids).push(") ")
                        .push(" AND tta.status = 'active' AND (tta.expires_at IS NULL OR tta.expires_at > NOW()) ");

                    builder
                        .push(" AND (t.subject_id IS NULL OR t.subject_id = ANY(")
                        .push_bind(subject_ids)
                        .push(")) ");

                    // 可选搜索条件
                    if let Some(search) = &params.search {
                        if !search.trim().is_empty() {
                            builder.push(" AND ");
                            let pattern = format!("%{}%", search.trim());
                            builder
                                .push(" (t.title ILIKE ")
                                .push_bind(pattern.clone())
                                .push(" OR t.id::text ILIKE ")
                                .push_bind(pattern.clone())
                                .push(") ");
                        }
                    }

                    // 可选学科条件
                    if let Some(code) = &params.subject_code {
                        if !code.trim().is_empty() {
                            builder.push(" AND ");
                            builder.push(" s.code = ").push_bind(code.to_string());
                        }
                    }

                    // 可选年级条件
                    if let Some(code) = &params.grade_level_code {
                        if !code.trim().is_empty() {
                            builder.push(" AND ");
                            builder.push(" g.code = ").push_bind(code.to_string());
                        }
                    }

                    // 可选版本
                    if let Some(version) = &params.resource_version {
                        if !version.trim().is_empty() {
                            builder.push(" AND ");
                            if version == "V1" {
                                // 数据库中 V1 对应 NULL
                                builder.push(" t.resource_version IS NULL ");
                            } else {
                                builder.push(" t.resource_version = ").push_bind(version.to_string());
                            }
                        }
                    }
                }

                let mut builder = Self::gen_textbook_vo();
                let mut count_builder = Self::gen_textbook_count();

                let pagination = PageParams {
                    page: params.page,
                    page_size: params.page_size,
                };
                let page = pagination.get_page();
                let page_size = pagination.get_page_size();
                let offset = pagination.get_offset();

                build_apply_filters(&mut builder, &tenant_ids, &ids, &params);
                build_apply_filters(&mut count_builder, &tenant_ids, &ids, &params);

                let total: i64 = count_builder
                    .build_query_scalar()
                    .fetch_one(pool)
                    .await?;

                builder.push(" LIMIT ")
                    .push_bind(page_size)
                    .push(" OFFSET ")
                    .push_bind(offset);

                let textbooks = builder.build_query_as().fetch_all(pool).await?;

                Ok(PageResult {
                    data: textbooks,
                    total,
                    page: page as i64,
                    page_size: page_size as i64,
                    total_pages: (total + page_size as i64 - 1) / page_size as i64,
                })
            }
        }
    }
    pub async fn bulk_insert_or_update_books<'e>(executor: &mut Transaction<'e, Postgres>, books: Vec<Textbook>) -> anyhow::Result<HashMap<i64, Uuid>> {
        let mut builder = QueryBuilder::new(r#"INSERT INTO public.textbooks
         (id, title, subject_id, grade_level_id, publisher, publication_year, isbn, version, status,
         cover_path, creator_id, created_at, updated_at, is_deleted, origin_id,
         subject_code, grade_level_code, distributor, edition, printing_version, authors, summary, resource_version) "#);
        builder.push_values(books, |mut b, book| {
            b.push_bind(book.id)
                .push_bind(book.title)
                .push_bind(book.subject_id)
                .push_bind(book.grade_level_id)
                .push_bind(book.publisher)
                .push_bind(book.publication_year)
                .push_bind(book.isbn)
                .push_bind(book.version)
                .push_bind(book.status)
                .push_bind(book.cover_path)
                .push_bind(book.creator_id)
                .push_bind(book.created_at)
                .push_bind(book.updated_at)
                .push_bind(book.is_deleted)
                .push_bind(book.origin_id)
                .push_bind(book.subject_code)
                .push_bind(book.grade_level_code)
                .push_bind(book.distributor)
                .push_bind(book.edition)
                .push_bind(book.printing_version)
                .push_bind(book.authors)
                .push_bind(book.summary)
                .push_bind(book.resource_version);
        });
        builder.push(r#" ON CONFLICT (origin_id) DO UPDATE SET
        title = EXCLUDED.title,
        publication_year = EXCLUDED.publication_year,
        isbn = EXCLUDED.isbn,
        cover_path = EXCLUDED.cover_path,
        subject_code = EXCLUDED.subject_code,
        grade_level_code = EXCLUDED.grade_level_code,
        distributor = EXCLUDED.distributor,
        edition = EXCLUDED.edition,
        printing_version = EXCLUDED.printing_version,
        resource_version = EXCLUDED.resource_version,
        updated_at = EXCLUDED.updated_at,
        is_deleted = EXCLUDED.is_deleted,
        publisher = EXCLUDED.publisher
        RETURNING id, origin_id
        "#);
        let list: Vec<(Uuid, i64)> = builder.build_query_as().fetch_all(&mut **executor).await?;
        let ret: HashMap<i64, Uuid> = list.into_iter().map(|(id, origin_id)| (origin_id, id)).collect();
        Ok(ret)
    }
    // pub async fn batch_bulk_insert_or_update_books<'e, E>(executor: E, books: Vec<Textbook>) -> anyhow::Result<Vec<(Uuid, i64)>>
    // where
    //     E: Executor<'e, Database = Postgres>,
    // {
    //     let batch_size = 1000;
    //     let mut ret = vec![];
    //     for batch in books.chunks(batch_size) {
    //         ret.extend(Self::bulk_insert_or_update_books(&executor, batch).await?);
    //     }
    //     Ok(ret)
    // }
} 