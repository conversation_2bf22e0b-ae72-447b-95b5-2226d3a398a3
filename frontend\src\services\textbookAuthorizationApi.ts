import  apiClient  from './apiClient';
import type {
  TextbookTenantAuthorization,
  TextbookTenantUsage,
  CreateAuthorizationRequest,
  UpdateAuthorizationRequest,
  AuthorizationQuery,
  AuthorizationsResponse,
  TenantTextbooksResponse,
  PermissionCheckResponse,
  AuditHistoryResponse,
} from '../types/textbookAuthorization.ts';

const BASE_URL = '/api/v1/textbook-authorizations';

export const textbookAuthorizationApi = {
  // 授权教辅书给租户
  grantAuthorization: async (data: CreateAuthorizationRequest): Promise<TextbookTenantAuthorization> => {
    const response = await apiClient.post(`${BASE_URL}`, data);
    return response.data.data;
  },

  // 撤销教辅书授权
  revokeAuthorization: async (textbookId: string, tenantId: string, reason?: string): Promise<void> => {
    const params = reason ? `?reason=${encodeURIComponent(reason)}` : '';
    await apiClient.post(`${BASE_URL}/revoke/${textbookId}/${tenantId}${params}`);
  },

  // 更新授权信息
  updateAuthorization: async (
    authorizationId: string,
    data: UpdateAuthorizationRequest
  ): Promise<TextbookTenantAuthorization> => {
    const response = await apiClient.put(`${BASE_URL}/${authorizationId}`, data);
    return response.data.data;
  },

  // 删除授权记录
  deleteAuthorization: async (authorizationId: string, reason?: string): Promise<void> => {
    const params = reason ? `?reason=${encodeURIComponent(reason)}` : '';
    await apiClient.delete(`${BASE_URL}/${authorizationId}${params}`);
  },

  // 获取授权列表
  getAuthorizations: async (query?: AuthorizationQuery): Promise<AuthorizationsResponse> => {
    const params = new URLSearchParams();
    if (query?.page) params.append('page', query.page.toString());
    if (query?.page_size) params.append('page_size', query.page_size.toString());
    if (query?.textbook_id) params.append('textbook_id', query.textbook_id);
    if (query?.tenant_id) params.append('tenant_id', query.tenant_id);
    if (query?.status) params.append('status', query.status);
    if (query?.search) params.append('search', query.search);

    const response = await apiClient.get(`${BASE_URL}?${params.toString()}`);
    return response.data;
  },

  // 检查权限
  checkPermission: async (
    textbookId: string,
    tenantId: string,
    permission: string
  ): Promise<PermissionCheckResponse> => {
    const response = await apiClient.get(`${BASE_URL}/check/${textbookId}/${tenantId}/${permission}`);
    return response.data.data;
  },

  // 获取租户已授权的教辅书
  getTenantAuthorizedTextbooks: async (
    tenantId: string,
    page?: number,
    pageSize?: number
  ): Promise<TenantTextbooksResponse> => {
    const params = new URLSearchParams();
    if (page) params.append('page', page.toString());
    if (pageSize) params.append('page_size', pageSize.toString());

    const response = await apiClient.get(`${BASE_URL}/tenant/${tenantId}?${params.toString()}`);
    return response.data.data;
  },

  // 获取授权审计历史
  getAuthorizationAuditHistory: async (
    authorizationId: string,
    page?: number,
    pageSize?: number
  ): Promise<AuditHistoryResponse> => {
    const params = new URLSearchParams();
    if (page) params.append('page', page.toString());
    if (pageSize) params.append('page_size', pageSize.toString());

    const response = await apiClient.get(`${BASE_URL}/${authorizationId}/audit?${params.toString()}`);
    return response.data.data;
  },

  // 记录访问
  recordAccess: async (textbookId: string, tenantId: string): Promise<void> => {
    await apiClient.post(`${BASE_URL}/access/${textbookId}/${tenantId}`);
  },

  // 记录下载
  recordDownload: async (textbookId: string, tenantId: string): Promise<void> => {
    await apiClient.post(`${BASE_URL}/download/${textbookId}/${tenantId}`);
  },

  // 获取使用统计
  getUsageStatistics: async (textbookId?: string, tenantId?: string): Promise<TextbookTenantUsage[]> => {
    const params = new URLSearchParams();
    if (textbookId) params.append('textbook_id', textbookId);
    if (tenantId) params.append('tenant_id', tenantId);

    const response = await apiClient.get(`${BASE_URL}/usage?${params.toString()}`);
    return response.data.data;
  },

  // 获取特定教辅书-租户使用记录
  getTextbookTenantUsage: async (textbookId: string, tenantId: string): Promise<TextbookTenantUsage | null> => {
    const response = await apiClient.get(`${BASE_URL}/usage/${textbookId}/${tenantId}`);
    return response.data.data;
  },
};

export default textbookAuthorizationApi;
