use std::collections::HashMap;
use sqlx::{<PERSON><PERSON><PERSON>, QueryBuilder, Transaction};
use uuid::Uuid;
use crate::model::public_resource::section::Section;

pub struct SectionRepository;

impl SectionRepository {
    pub async fn bulk_insert_or_update<'e>(executor: &mut Transaction<'e, Postgres>, sections: Vec<Section>) -> anyhow::Result<HashMap<i64, Uuid>> {
        let mut builder = QueryBuilder::new(r#"INSERT INTO public.sections
        (answer_card_id, items, snapshots, updated_at, origin_id, is_deleted)"#);
        builder.push_values(sections, |mut b, section| {
            b.push_bind(section.answer_card_id)
                .push_bind(section.items)
                .push_bind(section.snapshots)
                .push_bind(section.updated_at)
                .push_bind(section.origin_id)
                .push_bind(section.is_deleted);
        });
        builder.push(r#"ON CONFLICT (origin_id) DO UPDATE SET
        answer_card_id = EXCLUDED.answer_card_id,
        items = EXCLUDED.items,
        snapshots = EXCLUDED.snapshots,
        updated_at = EXCLUDED.updated_at,
        is_deleted = EXCLUDED.is_deleted
        RETURNING id, origin_id"#);
        let list: Vec<(Uuid, i64)> = builder.build_query_as().fetch_all(&mut **executor).await?;
        let ret: HashMap<i64, Uuid> = list.into_iter().map(|(id, origin_id)| (origin_id, id)).collect();
        Ok(ret)
    }
}