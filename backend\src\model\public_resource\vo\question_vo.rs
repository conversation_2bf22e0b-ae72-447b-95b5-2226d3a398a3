use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::types::<PERSON>son;
use crate::model::public_resource::question::{Question, QuestionUnit};
use crate::utils::date_time;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct QuestionJson {
    pub id: String,
    pub question_type_code: Option<String>,
    pub items: Vec<QuestionUnit>,
    #[serde(deserialize_with = "date_time::deserialize")]
    pub update_at: DateTime<Utc>,
    pub subject_code:Option<String>,
    #[serde(default)]
    pub is_deleted: bool,
}

impl From<QuestionJson> for Question {
    fn from(value: Question<PERSON><PERSON>) -> Self {
        let origin_id = value.id.parse().ok();
        Self {
            id: Default::default(),
            question_type_code: value.question_type_code.unwrap_or_default(),
            subject_code: value.subject_code.unwrap_or_default(),
            items: <PERSON><PERSON>(value.items),
            updated_at: Some(value.update_at),
            origin_id,
            is_deleted: value.is_deleted,
        }
    }
}