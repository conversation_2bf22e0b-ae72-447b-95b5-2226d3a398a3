import {FC, useEffect, useState} from "react";
import {BookVO, CatalogTreeNode, QuestionGroupData, SectionDetail} from "@/types/teachingAid.ts";
import {useIsMobile} from "@/hooks/use-mobile.ts";
import {ResizableHandle, ResizablePanel, ResizablePanelGroup} from "@/components/ui/resizable.tsx";
import {ScrollArea} from "@/components/ui/scroll-area.tsx";
import CatalogItem from "@/pages/TeachingAids/PreviewTest/CatalogItem.tsx";
import {chapterApi} from "@/services/teachingAidsApi.ts";
import {toast} from "sonner";

interface BookPreviewViewerProps {
    book: BookVO | null,
    catalogTrees: CatalogTreeNode[]
}

const BookPreviewViewer: FC<BookPreviewViewerProps> = ({
                                                           book,
                                                           catalogTrees
                                                       }) => {
    const isMobile = useIsMobile();

    const [activeCatalog, setActiveCatalog] = useState<CatalogTreeNode | null>(null);
    const [loadedCatalog, setLoadedCatalog] = useState<CatalogTreeNode | null>(null);
    const [section, setSection] = useState<SectionDetail | null>(null)

    const handleSelectChapter = (catalog: CatalogTreeNode) => {
        setActiveCatalog(catalog);

    };

    const loadSection = async (catalog: CatalogTreeNode) => {
        if (book && catalog) {
            let res = await chapterApi.getSectionV2(book.id, catalog.id);
            if (res.success) {
                if (res.data) {
                    setSection(res.data.section);
                    setLoadedCatalog(res.data.catalog);
                    if (res.data.catalog.id !== activeCatalog?.id)
                        toast.info('该章节无内容，正在跳转到：' + res.data.catalog.title)
                } else {
                    setSection(null);
                    setLoadedCatalog(activeCatalog)
                }
            }
        }
    };

    useEffect(() => {
        if (activeCatalog) {
            loadSection(activeCatalog);
        }
    }, [activeCatalog]);

    const headerView = (
        <div className='p-4 border-b flex items-center justify-between'>
            <div>
                <h1 className={`font-bold ${isMobile ? 'text-xl' : 'text-2xl'}`}>{book?.title}</h1>
                <p className={`text-muted-foreground ${isMobile ? 'text-xs' : 'text-sm'}`}>
                    {book?.subject} - {book?.gradeLevel}
                </p>
            </div>
            <div className="flex items-center gap-2">
                {loadedCatalog && (
                    <>
                        <div className="text-sm text-muted-foreground mr-2">
                            当前章节：{loadedCatalog.title}
                        </div>
                    </>
                )}
            </div>
        </div>
    );


    const catalogListView = (
        <ScrollArea className="h-full p-2">
            <div className="space-y-1">
                <div className="flex justify-between items-center p-2">
                    <h2 className="text-lg font-semibold">章节列表</h2>
                </div>
                {catalogTrees.map((catalog) => (
                    <CatalogItem
                        key={catalog.id}
                        chapter={catalog}
                        selectedChapter={loadedCatalog}
                        onSelectChapter={handleSelectChapter}
                        level={0}
                    />
                ))}
            </div>
        </ScrollArea>
    );

    const contentDisplayView = (
        <ScrollArea className="h-full p-6">
            {section ? (
                section.items.map((item, index) => {
                    if (item.key === "text") {
                        return <p key={index}>{item.value}</p>;
                    } else if (item.key === "questionGroup") {
                        const questionGroup = item.value as QuestionGroupData;
                        return (
                            <div key={index}>
                                {questionGroup.text && <p>{questionGroup.text}</p>}
                                {questionGroup.questions.map((question, questionIndex) => (
                                    <div key={questionIndex}>
                                        {/*todo 2025年9月12日 tuip：唯一问题在此处，question和question下的items没有对应前端的ts，由于有重复命名的结构体，使用时可以额外定义并且替换any*/}
                                        <p className="font-bold">{JSON.stringify(question.question)}</p>
                                    </div>
                                ))}
                            </div>
                        )
                    }
                })
            ) : (
                <p>本章节无文章</p>
            )}
        </ScrollArea>
    );


    return (
        <div className="h-full border rounded-lg">
            {headerView}
            <ResizablePanelGroup direction="horizontal" className="h-full w-full">
                <ResizablePanel defaultSize={20} minSize={15}>
                    {catalogListView}
                </ResizablePanel>
                <ResizableHandle withHandle/>
                <ResizablePanel defaultSize={80} minSize={15}>
                    {contentDisplayView}
                </ResizablePanel>
            </ResizablePanelGroup>
        </div>
    );
};

export default BookPreviewViewer;
