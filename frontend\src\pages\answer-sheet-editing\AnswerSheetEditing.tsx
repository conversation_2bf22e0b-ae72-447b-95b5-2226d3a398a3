import { usePageFullWidth } from '@/hooks/useLayoutWidth'
import React from 'react'
import { useParams } from 'react-router-dom'
import './AnswerSheetEditing.scss'
import { PublicSheet } from './components/public-sheet/PublicSheet'
import { TenantSheet } from './components/tenant-sheet/TenantSheet'

/**
 * 作者：张瀚
 * 说明：答题卡编辑主页面，根据版本不同切换不同的渲染逻辑
 */
const AnswerSheetEditing: React.FC = () => {
  usePageFullWidth()
  const { id = '', schema_name = '' } = useParams<{ schema_name: string; id: string }>()
  return <>{schema_name === 'public' ? <PublicSheet answerId={id}></PublicSheet> : <TenantSheet paperId={id} schemaName={schema_name}></TenantSheet>}</>
}
export default AnswerSheetEditing
