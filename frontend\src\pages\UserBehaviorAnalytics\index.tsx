import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { usePageFullWidth } from '@/hooks/useLayoutWidth';
import { toast } from 'sonner';
import { AnalyticsApiService } from '@/services/analyticsApi';
import type {
  AnalyticsOverview,
  RealTimeMetrics,
  OnlineUserStats,
  UserBehaviorTrend
} from '@/services/analyticsApi';

// 导入拆分的组件
import {
  PageHeader,
  OverviewCards,
  RealTimeMetricsCards,
  UserStatusDistribution,
  ModuleUsageChart,
  DeviceDistribution,
  HotPagesChart,
  ActiveTimeChart,
  BehaviorTrendsChart
} from './components';

const UserBehaviorAnalytics: React.FC = () => {
  usePageFullWidth();

  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('today');
  const [overview, setOverview] = useState<AnalyticsOverview | null>(null);
  const [realTimeMetrics, setRealTimeMetrics] = useState<RealTimeMetrics | null>(null);
  const [onlineStats, setOnlineStats] = useState<OnlineUserStats | null>(null);
  const [behaviorTrends, setBehaviorTrends] = useState<UserBehaviorTrend[]>([]);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);

  const analyticsApi = new AnalyticsApiService();

  // 获取概览数据
  const fetchOverview = async () => {
    try {
      const data = await analyticsApi.getAnalyticsOverview(timeRange);
      setOverview(data);
      setLastUpdateTime(new Date());
    } catch (error) {
      console.error('获取概览数据失败:', error);
      toast.error('获取概览数据失败');
    }
  };

  // 获取实时指标
  const fetchRealTimeMetrics = async () => {
    try {
      const data = await analyticsApi.getRealTimeMetricsNew();
      setRealTimeMetrics(data);
      setLastUpdateTime(new Date());
    } catch (error) {
      console.error('获取实时指标失败:', error);
      toast.error('获取实时指标失败');
    }
  };

  // 获取在线用户统计
  const fetchOnlineStats = async () => {
    try {
      const data = await analyticsApi.getOnlineUserStats();
      setOnlineStats(data);
    } catch (error) {
      console.error('获取在线用户统计失败:', error);
      toast.error('获取在线用户统计失败');
    }
  };

  // 获取行为趋势
  const fetchBehaviorTrends = async () => {
    try {
      const data = await analyticsApi.getUserBehaviorTrends(timeRange);
      setBehaviorTrends(data);
    } catch (error) {
      console.error('获取行为趋势失败:', error);
      toast.error('获取行为趋势失败');
    }
  };

  // 刷新所有数据
  const refreshData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchOverview(),
        fetchRealTimeMetrics(),
        fetchOnlineStats(),
        fetchBehaviorTrends()
      ]);
    } finally {
      setLoading(false);
    }
  };

  // 处理时间范围变更
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
  };

  // 初始化数据
  useEffect(() => {
    refreshData();
  }, []);

  // 时间范围变更时重新获取数据
  useEffect(() => {
    if (timeRange) {
      refreshData();
    }
  }, [timeRange]);

  // 定时刷新实时数据（2分钟，并检测页面可见性）
  useEffect(() => {
    let interval: number;

    const startPolling = () => {
      interval = setInterval(() => {
        // 只在页面可见时轮询
        if (!document.hidden) {
          fetchRealTimeMetrics();
        }
      }, 120000); // 改为2分钟轮询
    };

    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面不可见时清除定时器
        if (interval) {
          clearInterval(interval);
        }
      } else {
        // 页面重新可见时恢复轮询，并立即更新一次
        fetchRealTimeMetrics();
        startPolling();
      }
    };

    // 初始启动
    startPolling();

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      if (interval) {
        clearInterval(interval);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 工具函数
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getTimeRangeLabel = (range: string): string => {
    const labels: Record<string, string> = {
      today: '昨日',
      yesterday: '前日',
      week: '上周',
      month: '上月'
    };
    return labels[range] || '历史同期';
  };

  // 加载骨架屏
  if (loading && !overview) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex justify-between items-center">
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="flex space-x-4">
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader
        timeRange={timeRange}
        onTimeRangeChange={handleTimeRangeChange}
        onRefresh={refreshData}
        lastUpdateTime={lastUpdateTime}
      />

      {/* 核心指标卡片 */}
      {overview && (
        <OverviewCards
          overview={overview}
          timeRange={timeRange}
          formatNumber={formatNumber}
          getTimeRangeLabel={getTimeRangeLabel}
        />
      )}

      {/* 实时指标卡片 */}
      {realTimeMetrics && (
        <RealTimeMetricsCards realTimeMetrics={realTimeMetrics} />
      )}

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="behavior">行为分析</TabsTrigger>
          <TabsTrigger value="trends">趋势统计</TabsTrigger>
        </TabsList>

        {/* 总览 Tab */}
        <TabsContent value="overview">
          {onlineStats && (
            <Card>
              <CardContent className="p-6">
                <div className="text-2xl font-bold mb-4">{onlineStats.total_online} 在线用户</div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <UserStatusDistribution statusData={onlineStats.by_status} />
                  <ModuleUsageChart
                    moduleData={onlineStats.by_module}
                    totalUsers={onlineStats.total_online}
                  />
                  <DeviceDistribution deviceData={onlineStats.by_device} />
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* 行为分析 Tab */}
        <TabsContent value="behavior">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <HotPagesChart timeRange={timeRange} />
            <ActiveTimeChart timeRange={timeRange} />
          </div>
        </TabsContent>

        {/* 趋势统计 Tab */}
        <TabsContent value="trends">
          <BehaviorTrendsChart
            behaviorTrends={behaviorTrends}
            timeRange={timeRange}
            getTimeRangeLabel={getTimeRangeLabel}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UserBehaviorAnalytics; 