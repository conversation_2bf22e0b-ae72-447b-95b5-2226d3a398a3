import { PaperVO } from '@/components/question-card/v1.0.0/entity/paper-vo'
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils'
import { publicPaperApi } from '@/services/publicPapersApi'
import { Paper } from '@/types/papers'
import { useEffect, useState } from 'react'
import { PublicSheet_0_0_0 } from './v0.0.0/PublicSheet_0_0_0'

export interface Props {
  answerId: string
}

/**
 * 作者：张瀚
 * 说明：教辅内的题卡编辑
 */
export function PublicSheet({ answerId }: Props) {
  const tenantInfo = getTenantInfoFromLocalStorage()
  const [nowTenantId, setNowTenantId] = useState(tenantInfo?.tenant_id)
  if (nowTenantId !== tenantInfo?.tenant_id) {
    setNowTenantId(tenantInfo?.tenant_id)
  }
  //试卷数据
  const [paper, setPaper] = useState<Paper | undefined>(undefined)
  //答题卡版本，老版本是old，其他是版本号
  const [answerCardVersion, setAnswerCardVersion] = useState<string>(PublicSheet_0_0_0.Version)
  //查询数据
  useEffect(() => {
    if (!nowTenantId) {
      return undefined
    }
    publicPaperApi.findByAnswerId(nowTenantId, answerId).then((res) => {
      const { data } = res
      if (data) {
        setPaper(data)
        //判断版本号
        const { paper_content } = data
        let paperVO = paper_content as PaperVO
        if (!paperVO.answerCard) {
          //老版本
          setAnswerCardVersion(PublicSheet_0_0_0.Version)
        } else {
          //新版本
          setAnswerCardVersion(paperVO.answerCard.version)
        }
      }
    })
  }, [answerId, nowTenantId])
  if (!paper) {
    return
  }
  switch (answerCardVersion) {
    case PublicSheet_0_0_0.Version:
      return <PublicSheet_0_0_0.Editor paper={paper} nowTenantId={nowTenantId ?? ''}></PublicSheet_0_0_0.Editor>
  }
  return <>不支持的版本：{answerCardVersion}</>
}
