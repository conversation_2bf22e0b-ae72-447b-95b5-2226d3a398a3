use anyhow::Result;
use chrono::{DateTime, Utc};
use sqlx::PgPool;
use uuid::Uuid;

use crate::model::analytics::user_behavior_model::*;
use crate::repository::analytics::user_behavior_repository::UserBehaviorRepository;
use crate::middleware::auth_middleware::AuthContext;

/// 用户行为分析服务
pub struct UserBehaviorService {
    repository: UserBehaviorRepository,
}

impl UserBehaviorService {
    pub fn new(pool: PgPool) -> Self {
        Self {
            repository: UserBehaviorRepository::new(pool),
        }
    }

    /// 记录用户行为日志
    pub async fn log_behavior(&self, request: CreateBehaviorLogRequest) -> Result<Uuid> {
        self.repository.create_behavior_log(request).await
    }

    /// 获取用户行为日志列表
    pub async fn get_behavior_logs(
        &self,
        params: &BehaviorLogQueryParams,
        _context: &AuthContext,
    ) -> Result<Vec<UserBehaviorLog>> {
        // 根据用户权限过滤数据
        let filtered_params = self.apply_permission_filter(params, _context).await?;
        
        self.repository.get_behavior_logs(&filtered_params).await
    }

    /// 获取用户行为分析数据
    pub async fn get_behavior_analytics(
        &self,
        tenant_id: Option<Uuid>,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
        context: &AuthContext,
    ) -> Result<BehaviorAnalyticsData> {
        // 权限检查：确保用户只能查看其有权限的租户数据
        let authorized_tenant_id = self.get_authorized_tenant_id(tenant_id, context).await?;
        
        self.repository
            .get_behavior_analytics(authorized_tenant_id, start_date, end_date)
            .await
    }

    /// 获取用户行为趋势数据
    pub async fn get_behavior_trends(
        &self,
        tenant_id: Option<Uuid>,
        days: i32,
        context: &AuthContext,
    ) -> Result<Vec<UserBehaviorTrend>> {
        let authorized_tenant_id = self.get_authorized_tenant_id(tenant_id, context).await?;
        
        self.repository
            .get_behavior_trends(authorized_tenant_id, days)
            .await
    }

    /// 获取实时行为指标
    pub async fn get_real_time_metrics(
        &self,
        tenant_id: Option<Uuid>,
        context: &AuthContext,
    ) -> Result<RealTimeBehaviorMetrics> {
        let authorized_tenant_id = self.get_authorized_tenant_id(tenant_id, context).await?;
        
        self.repository
            .get_real_time_metrics(authorized_tenant_id)
            .await
    }

    /// 根据用户权限过滤查询参数
    async fn apply_permission_filter(
        &self,
        params: &BehaviorLogQueryParams,
        context: &AuthContext,
    ) -> Result<BehaviorLogQueryParams> {
        let mut filtered_params = params.clone();
        
        // 如果用户不是超级管理员，只允许查看自己租户的数据
        if !self.is_super_admin(context).await? {
            filtered_params.tenant_id = context.tenant_links.first().map(|link| link.tenant_id);
            
            // 普通用户只能查看自己的行为日志
            if !self.is_tenant_admin(context).await? {
                filtered_params.user_id = Some(context.user_id);
            }
        }
        
        Ok(filtered_params)
    }

    /// 获取用户有权限查看的租户ID
    async fn get_authorized_tenant_id(
        &self,
        requested_tenant_id: Option<Uuid>,
        context: &AuthContext,
    ) -> Result<Option<Uuid>> {
        // 超级管理员可以查看所有租户数据
        if self.is_super_admin(context).await? {
            return Ok(requested_tenant_id);
        }
        
        // 其他用户只能查看自己租户的数据
        Ok(context.tenant_links.first().map(|link| link.tenant_id))
    }

    /// 检查是否为超级管理员
    async fn is_super_admin(&self, context: &AuthContext) -> Result<bool> {
        // 这里应该从数据库或缓存中检查用户角色
        // 为了简化，暂时返回false
        // TODO: 实现实际的权限检查逻辑
        Ok(false)
    }

    /// 检查是否为租户管理员
    async fn is_tenant_admin(&self, context: &AuthContext) -> Result<bool> {
        // 这里应该检查用户在当前租户中是否有管理员权限
        // TODO: 实现实际的权限检查逻辑
        Ok(false)
    }

    /// 分析用户行为模式
    pub async fn analyze_user_patterns(
        &self,
        user_id: Uuid,
        tenant_id: Option<Uuid>,
        days: i32,
        context: &AuthContext,
    ) -> Result<UserBehaviorPattern> {
        // 权限检查：用户只能查看自己的行为模式，或管理员查看下属用户
        if !self.can_view_user_behavior(user_id, context).await? {
            return Err(anyhow::anyhow!("没有权限查看该用户的行为模式"));
        }

        // TODO: 实现用户行为模式分析
        Ok(UserBehaviorPattern {
            user_id,
            analysis_period_days: days,
            most_active_hours: vec![9, 10, 14, 15],
            most_used_modules: vec!["exam".to_string(), "homework".to_string()],
            avg_session_duration_minutes: 45.0,
            total_sessions: 12,
            total_actions: 156,
            behavior_score: 85.0,
        })
    }

    /// 检查是否可以查看用户行为
    async fn can_view_user_behavior(&self, target_user_id: Uuid, context: &AuthContext) -> Result<bool> {
        // 用户可以查看自己的行为
        if context.user_id == target_user_id {
            return Ok(true);
        }
        
        // 管理员可以查看下属用户的行为
        // TODO: 实现管理员权限检查
        Ok(false)
    }
}

/// 用户行为模式分析结果
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct UserBehaviorPattern {
    pub user_id: Uuid,
    pub analysis_period_days: i32,
    pub most_active_hours: Vec<i32>,
    pub most_used_modules: Vec<String>,
    pub avg_session_duration_minutes: f64,
    pub total_sessions: i32,
    pub total_actions: i32,
    pub behavior_score: f64,
} 