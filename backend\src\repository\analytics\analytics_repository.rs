use sqlx::{PgPool, Row};
use anyhow::Result;
use chrono::{DateTime, Utc, NaiveDate};
use uuid::Uuid;

use crate::model::analytics::analytics_dto::*;

/// 综合分析数据访问层
pub struct AnalyticsRepository {
    pool: PgPool,
}

impl AnalyticsRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 获取分析总览数据
    pub async fn get_analytics_overview(&self, tenant_id: Option<Uuid>) -> Result<AnalyticsOverview> {
        let tenant_condition = if tenant_id.is_some() {
            "WHERE tenant_id = $1"
        } else {
            ""
        };

        // 总用户数
        let total_users_query = format!(
            "SELECT COUNT(DISTINCT user_id)::bigint FROM user_behavior_logs {}",
            tenant_condition
        );

        let total_users = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, i64>(&total_users_query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, i64>(&total_users_query)
                .fetch_one(&self.pool)
                .await?
        };

        // 今日活跃用户
        let active_users_today_query = format!(
            "SELECT COUNT(DISTINCT user_id)::bigint FROM user_behavior_logs WHERE DATE(created_at) = CURRENT_DATE {}",
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let active_users_today = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, i64>(&active_users_today_query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, i64>(&active_users_today_query)
                .fetch_one(&self.pool)
                .await?
        };

        // 今日会话数
        let sessions_today_query = format!(
            "SELECT COUNT(DISTINCT session_id)::bigint FROM user_behavior_logs WHERE DATE(created_at) = CURRENT_DATE {}",
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let total_sessions_today = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, i64>(&sessions_today_query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, i64>(&sessions_today_query)
                .fetch_one(&self.pool)
                .await?
        };

        // 今日页面浏览量
        let page_views_today_query = format!(
            "SELECT COUNT(*)::bigint FROM user_behavior_logs WHERE DATE(created_at) = CURRENT_DATE AND action_type = 'page_view' {}",
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let total_page_views_today = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, i64>(&page_views_today_query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, i64>(&page_views_today_query)
                .fetch_one(&self.pool)
                .await?
        };

        // 今日总行为数
        let actions_today_query = format!(
            "SELECT COUNT(*)::bigint FROM user_behavior_logs WHERE DATE(created_at) = CURRENT_DATE {}",
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let total_actions_today = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, i64>(&actions_today_query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, i64>(&actions_today_query)
                .fetch_one(&self.pool)
                .await?
        };

        // 当前在线用户数
        let online_users_query = format!(
            "SELECT COUNT(DISTINCT user_id)::bigint FROM online_users WHERE status = 'online' AND last_activity_at >= NOW() - INTERVAL '5 minutes' {}",
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let current_online_users = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, i64>(&online_users_query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, i64>(&online_users_query)
                .fetch_one(&self.pool)
                .await?
        };

        // 今日峰值并发数
        let peak_concurrent_query = format!(
            r#"
            SELECT COALESCE(MAX(hourly_count), 0)::bigint
            FROM (
                SELECT date_trunc('hour', login_at) as hour, COUNT(DISTINCT user_id) as hourly_count
                FROM online_users
                WHERE DATE(login_at) = CURRENT_DATE {}
                GROUP BY date_trunc('hour', login_at)
            ) hourly_stats
            "#,
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let peak_concurrent_today = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, i64>(&peak_concurrent_query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, i64>(&peak_concurrent_query)
                .fetch_one(&self.pool)
                .await?
        };

        // 平均会话时长
        let avg_session_query = format!(
            "SELECT COALESCE(AVG(session_duration_seconds), 0)::float8 / 60.0 FROM online_users WHERE session_duration_seconds > 0 {}",
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let avg_session_duration_minutes = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, f64>(&avg_session_query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, f64>(&avg_session_query)
                .fetch_one(&self.pool)
                .await?
        };

        // 计算昨日数据用于增长百分比计算
        
        // 昨日活跃用户数
        let active_users_yesterday_query = format!(
            "SELECT COUNT(DISTINCT user_id)::bigint FROM user_behavior_logs WHERE DATE(created_at) = CURRENT_DATE - INTERVAL '1 day' {}",
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let active_users_yesterday = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, i64>(&active_users_yesterday_query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await.unwrap_or(0)
        } else {
            sqlx::query_scalar::<_, i64>(&active_users_yesterday_query)
                .fetch_one(&self.pool)
                .await.unwrap_or(0)
        };

        // 昨日页面浏览量
        let page_views_yesterday_query = format!(
            "SELECT COUNT(*)::bigint FROM user_behavior_logs WHERE DATE(created_at) = CURRENT_DATE - INTERVAL '1 day' AND action_type = 'page_view' {}",
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let page_views_yesterday = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, i64>(&page_views_yesterday_query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await.unwrap_or(0)
        } else {
            sqlx::query_scalar::<_, i64>(&page_views_yesterday_query)
                .fetch_one(&self.pool)
                .await.unwrap_or(0)
        };

        // 计算增长百分比
        let user_growth_percentage = if active_users_yesterday > 0 {
            ((active_users_today - active_users_yesterday) as f64 / active_users_yesterday as f64) * 100.0
        } else if active_users_today > 0 {
            100.0
        } else {
            0.0
        };

        let active_users_growth_percentage = user_growth_percentage; // 使用相同的计算

        let page_views_growth_percentage = if page_views_yesterday > 0 {
            ((total_page_views_today - page_views_yesterday) as f64 / page_views_yesterday as f64) * 100.0
        } else if total_page_views_today > 0 {
            100.0
        } else {
            0.0
        };

        Ok(AnalyticsOverview {
            total_users,
            active_users_today,
            total_sessions_today,
            avg_session_duration_minutes,
            total_page_views_today,
            total_actions_today,
            current_online_users,
            peak_concurrent_today,
            user_growth_percentage,
            active_users_growth_percentage,
            page_views_growth_percentage,
        })
    }

    /// 获取实时指标数据
    pub async fn get_real_time_metrics(&self, tenant_id: Option<Uuid>) -> Result<RealTimeMetrics> {
        // 当前在线用户数
        let current_online = self.get_current_online_count(tenant_id).await?;

        // 最近5分钟活跃用户
        let active_5min = self.get_recent_active_users(tenant_id, 5).await?;

        // 最近15分钟活跃用户
        let active_15min = self.get_recent_active_users(tenant_id, 15).await?;

        // 最近1小时活跃用户
        let active_1hour = self.get_recent_active_users(tenant_id, 60).await?;

        // 最近1小时行为数
        let actions_last_hour = self.get_recent_actions(tenant_id, 60).await?;

        // 平均响应时间（最近1小时）
        let avg_response_time_ms = self.get_avg_response_time(tenant_id, 60).await?;

        // 错误率（最近1小时）
        let error_rate_percent = self.get_error_rate(tenant_id, 60).await?;

        // 新会话数（最近1小时）
        let new_sessions_last_hour = self.get_new_sessions(tenant_id, 60).await?;

        Ok(RealTimeMetrics {
            current_online,
            active_last_5min: active_5min,
            active_last_15min: active_15min,
            active_last_hour: active_1hour,
            actions_last_hour,
            avg_response_time_ms,
            error_rate_percent,
            new_sessions_last_hour,
        })
    }

    // 辅助方法
    async fn get_current_online_count(&self, tenant_id: Option<Uuid>) -> Result<i64> {
        let query = if tenant_id.is_some() {
            "SELECT COUNT(DISTINCT user_id)::bigint FROM online_users WHERE status = 'online' AND tenant_id = $1 AND last_activity_at >= NOW() - INTERVAL '5 minutes'"
        } else {
            "SELECT COUNT(DISTINCT user_id)::bigint FROM online_users WHERE status = 'online' AND last_activity_at >= NOW() - INTERVAL '5 minutes'"
        };

        let count = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, Option<i64>>(query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, Option<i64>>(query)
                .fetch_one(&self.pool)
                .await?
        };

        Ok(count.unwrap_or(0))
    }

    async fn get_recent_active_users(&self, tenant_id: Option<Uuid>, minutes: i32) -> Result<i64> {
        let query = format!(
            "SELECT COUNT(DISTINCT user_id)::bigint FROM online_users WHERE last_activity_at >= NOW() - INTERVAL '{} minutes' {}",
            minutes,
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let count = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, Option<i64>>(&query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, Option<i64>>(&query)
                .fetch_one(&self.pool)
                .await?
        };

        Ok(count.unwrap_or(0))
    }

    async fn get_recent_actions(&self, tenant_id: Option<Uuid>, minutes: i32) -> Result<i64> {
        let query = format!(
            "SELECT COUNT(*)::bigint FROM user_behavior_logs WHERE created_at >= NOW() - INTERVAL '{} minutes' {}",
            minutes,
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let count = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, Option<i64>>(&query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, Option<i64>>(&query)
                .fetch_one(&self.pool)
                .await?
        };

        Ok(count.unwrap_or(0))
    }

    async fn get_avg_response_time(&self, tenant_id: Option<Uuid>, minutes: i32) -> Result<f64> {
        let query = format!(
            "SELECT COALESCE(AVG(response_time_ms), 0)::float8 FROM user_behavior_logs WHERE response_time_ms IS NOT NULL AND created_at >= NOW() - INTERVAL '{} minutes' {}",
            minutes,
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let avg_time = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, f64>(&query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, f64>(&query)
                .fetch_one(&self.pool)
                .await?
        };

        Ok(avg_time)
    }

    async fn get_error_rate(&self, tenant_id: Option<Uuid>, minutes: i32) -> Result<f64> {
        let query = format!(
            r#"
            SELECT CASE 
                WHEN COUNT(*) = 0 THEN 0.0
                ELSE (COUNT(CASE WHEN success = false THEN 1 END)::float8 / COUNT(*)::float8 * 100.0)
            END
            FROM user_behavior_logs 
            WHERE created_at >= NOW() - INTERVAL '{} minutes' {}
            "#,
            minutes,
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let error_rate = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, f64>(&query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, f64>(&query)
                .fetch_one(&self.pool)
                .await?
        };

        Ok(error_rate)
    }

    async fn get_new_sessions(&self, tenant_id: Option<Uuid>, minutes: i32) -> Result<i64> {
        let query = format!(
            "SELECT COUNT(DISTINCT session_id)::bigint FROM online_users WHERE login_at >= NOW() - INTERVAL '{} minutes' {}",
            minutes,
            if tenant_id.is_some() { "AND tenant_id = $1" } else { "" }
        );

        let count = if let Some(tid) = tenant_id {
            sqlx::query_scalar::<_, Option<i64>>(&query)
                .bind(tid)
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar::<_, Option<i64>>(&query)
                .fetch_one(&self.pool)
                .await?
        };

        Ok(count.unwrap_or(0))
    }
} 