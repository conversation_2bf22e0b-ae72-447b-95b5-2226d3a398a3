use std::collections::HashMap;
use sqlx::{Pg<PERSON><PERSON>, <PERSON>gres, QueryBuilder, Transaction};
use uuid::Uuid;
use crate::model::public_resource::question_answer::QuestionAnswer;

pub struct QuestionAnswerRepository;

impl QuestionAnswerRepository {
    pub async fn fetch_question_answers(pool: &PgPool, answer_ids: Vec<Uuid>) -> anyhow::Result<Vec<QuestionAnswer>> {
        let mut builder = QueryBuilder::new("SELECT * FROM public.question_answers WHERE id = ANY(");
        builder.push_bind(answer_ids).push(")");
        let answers = builder.build_query_as().fetch_all(pool).await?;
        Ok(answers)
    }
    pub async fn bulk_insert_or_update<'e>(executor: &mut Transaction<'e, Postgres>, answers: Vec<QuestionAnswer>) -> anyhow::Result<HashMap<i64, Uuid>> {
        let mut builder = QueryBuilder::new(r#"INSERT INTO public.question_answers
        (question_id, answer_area_id, content, explanation, updated_at, origin_id, is_deleted) "#);
        builder.push_values(answers, |mut b, answer| {
            b.push_bind(answer.question_id)
            .push_bind(answer.answer_area_id)
            .push_bind(answer.content)
            .push_bind(answer.explanation)
            .push_bind(answer.updated_at)
            .push_bind(answer.origin_id)
            .push_bind(answer.is_deleted);
        });
        builder.push(r#"ON CONFLICT (origin_id) DO UPDATE SET
        question_id = EXCLUDED.question_id,
        answer_area_id = EXCLUDED.answer_area_id,
        content = EXCLUDED.content,
        explanation = EXCLUDED.explanation,
        updated_at = EXCLUDED.updated_at,
        is_deleted = EXCLUDED.is_deleted
        RETURNING id, origin_id"#);
        let list: Vec<(Uuid, i64)> = builder.build_query_as().fetch_all(&mut **executor).await?;
        let ret: HashMap<i64, Uuid> = list.into_iter().map(|(id, origin_id)| (origin_id, id)).collect();
        Ok(ret)
    }
}