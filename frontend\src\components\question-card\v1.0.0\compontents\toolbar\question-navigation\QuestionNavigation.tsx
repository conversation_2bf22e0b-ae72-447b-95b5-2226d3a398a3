import { useContext } from 'react'
import { useStore } from 'zustand'
import { PaperContentKeyEnum, PaperContentVO, QuestionGroupDataVO, QuestionVO } from '../../../entity/paper-vo'
import { getAnswerAreaId, PaperDataStoreContext, traverseAnswerArea } from '../../../store/paperDataStore'
import { QuestionCardUI } from '../../../ui/question-card-components/global'
import { PropsDrawerItem } from '../../base-component/question-item-edit-drawer/EditDrawer'
import styles from './QuestionNavigation.module.scss'
import { AddQuestionBlock, AddQuestionDialog } from './components/AddQuestionDialog'

/**
 * 作者：张瀚
 * 说明：工具栏题目导航部分
 * 按试卷模块划分内容
 */
export default function QuestionNavigation() {
  const usePaperDataStoreContext = useContext(PaperDataStoreContext)
  const paperContents = useStore(usePaperDataStoreContext, (state) => state.paperContents)
  return (
    <>
      <ScoreCount></ScoreCount>
      <AddQuestionDialog></AddQuestionDialog>
      {paperContents.map((paperContent, paperContentIndex) => {
        switch (paperContent.key) {
          case PaperContentKeyEnum.Text:
            return <PaperContentTextPart key={paperContentIndex} paperContentValue={paperContent.value as string} paperContentIndex={paperContentIndex}></PaperContentTextPart>
          case PaperContentKeyEnum.QuestionGroup:
            return <PaperContentQuestionGroupPart key={paperContentIndex} paperContent={paperContent} paperContentIndex={paperContentIndex}></PaperContentQuestionGroupPart>
        }
      })}
    </>
  )
}

function ScoreCount() {
  const usePaperDataStoreContext = useContext(PaperDataStoreContext)
  const paperContents = useStore(usePaperDataStoreContext, (state) => state.paperContents)
  const answerAreaIdToCriterionIdMap = useStore(usePaperDataStoreContext, (state) => state.answerAreaIdToCriterionIdMap)
  const criterionIdToCriterionMap = useStore(usePaperDataStoreContext, (state) => state.criterionIdToCriterionMap)
  let score = 0
  paperContents.forEach((paperContent) => {
    traverseAnswerArea(paperContent, (answerArea) => {
      const answerAreaId = getAnswerAreaId(answerArea.questionId, answerArea.id)
      const criterion = criterionIdToCriterionMap.get(answerAreaIdToCriterionIdMap.get(answerAreaId) ?? '')
      if (criterion) {
        score += criterion.score ?? 0
      }
    })
  })
  return (
    <>
      <PropsDrawerItem title={'试卷总分'}>{score} 分</PropsDrawerItem>
    </>
  )
}

function PaperContentTextPart(props: { paperContentValue: string; paperContentIndex: number }) {
  const { paperContentIndex } = props
  return (
    <>
      <QuestionCardUI.Divider title={`${paperContentIndex + 1} - 文本`} style={{ borderColor: '#7cb305' }}></QuestionCardUI.Divider>
      <div>文本内容</div>
    </>
  )
}

/**
 * 作者：张瀚
 * 说明：题组模块
 */
function PaperContentQuestionGroupPart(props: { paperContent: PaperContentVO; paperContentIndex: number }) {
  const { paperContent, paperContentIndex } = props
  const paperContentValue = paperContent.value as QuestionGroupDataVO
  const usePaperDataStoreContext = useContext(PaperDataStoreContext)
  const answerAreaIdToCriterionIdMap = useStore(usePaperDataStoreContext, (state) => state.answerAreaIdToCriterionIdMap)
  const criterionIdToCriterionMap = useStore(usePaperDataStoreContext, (state) => state.criterionIdToCriterionMap)
  let score = 0
  traverseAnswerArea(paperContent, (answerArea) => {
    const answerAreaId = getAnswerAreaId(answerArea.questionId, answerArea.id)
    const criterion = criterionIdToCriterionMap.get(answerAreaIdToCriterionIdMap.get(answerAreaId) ?? '')
    if (criterion) {
      score += criterion.score ?? 0
    }
  })
  return (
    <>
      <QuestionCardUI.Divider title={`${paperContentIndex + 1} - 题组(${score}分)`} style={{ borderColor: '#7cb305' }}></QuestionCardUI.Divider>
      {paperContentValue.text ?? ''}
      <QuestionCardUI.Flex gap={10} align='center'>
        {paperContentValue.questions.map((paperQuestion) => {
          const { question } = paperQuestion
          if (!question) {
            return
          }
          return <QuestionBlock key={question.id} question={question}></QuestionBlock>
        })}
        <AddQuestionBlock questionGroup={paperContentValue} paperContent={paperContent} paperContentIndex={paperContentIndex}></AddQuestionBlock>
      </QuestionCardUI.Flex>
    </>
  )
}

/**
 * 作者：张瀚
 * 说明：题目格子
 */
function QuestionBlock(props: { question: QuestionVO }) {
  const { question } = props
  const { questionNo, id } = question
  let className = styles['question-block']
  let tips = ''
  if (!questionNo) {
    className += ` ${styles['warning']}`
    tips += '缺失题号\n'
  }
  if (tips.length === 0) {
    tips = '数据完整'
  }
  const onClickHandler = () => {
    //点击后把该题目的第一个题块滚动到页面内
    const visibleLayerDom = document.getElementsByClassName('canvas-visible-layer-dom-class')[0]
    const dom = document.querySelector(`[data-question-id="${id}"]`)
    if (!visibleLayerDom || !dom) {
      return
    }
    const { top } = dom.getBoundingClientRect()
    const { top: vt } = visibleLayerDom.getBoundingClientRect()
    visibleLayerDom.scrollTo({ top: Math.round(visibleLayerDom.scrollTop + top - vt) })
  }
  return (
    <>
      <div className={className} title={tips} onClick={onClickHandler}>
        {questionNo ?? '-'}
      </div>
    </>
  )
}
