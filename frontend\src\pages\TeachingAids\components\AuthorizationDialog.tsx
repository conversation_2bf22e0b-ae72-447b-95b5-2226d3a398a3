import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { MultiSelect, Option } from '@/components/ui/multi-select';
import { Download, Eye, Loader2, Share2 } from 'lucide-react';
import { toast } from 'sonner';
import textbookAuthorizationApi from '@/services/textbookAuthorizationApi.ts';
import { getTenantSummaries } from '@/services/tenantApi';
import { TeachingAid } from '@/services/teachingAidsApi';

interface AuthorizationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedAid: TeachingAid | null;
  onAuthorizationSuccess?: () => void;
}

interface AuthorizationForm {
  selected_tenants: Option[];
  expires_at: string;
  permissions: {
    read: boolean;
    download: boolean;
    distribute: boolean;
  };
  notes: string;
}

const AuthorizationDialog: React.FC<AuthorizationDialogProps> = ({
  isOpen,
  onClose,
  selectedAid,
  onAuthorizationSuccess,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingTenants, setIsLoadingTenants] = useState(false);
  const [tenantOptions, setTenantOptions] = useState<Option[]>([]);
  const [form, setForm] = useState<AuthorizationForm>({
    selected_tenants: [],
    expires_at: '',
    permissions: {
      read: true,
      download: false,
      distribute: false,
    },
    notes: '',
  });

  const resetForm = () => {
    setForm({
      selected_tenants: [],
      expires_at: '',
      permissions: {
        read: true,
        download: false,
        distribute: false,
      },
      notes: '',
    });
  };

  const fetchTenants = async () => {
    try {
      setIsLoadingTenants(true);
      const response = await getTenantSummaries();
      const tenants = response.data || [];
      
      // Transform tenants to MultiSelect options
      const options: Option[] = tenants.map(tenant => ({
        value: tenant.id,
        label: `${tenant.name} (${tenant.tenantType})`,
        // Store additional tenant info for display
        tenantId: tenant.id,
        tenantName: tenant.name,
        tenantType: tenant.tenantType,
        status: tenant.status
      }));
      
      setTenantOptions(options);
    } catch (error) {
      console.error('Error fetching tenants:', error);
      toast.error('获取教育机构列表失败');
    } finally {
      setIsLoadingTenants(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchTenants();
    }
  }, [isOpen]);

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleGrantAuthorization = async () => {
    if (!selectedAid || form.selected_tenants.length === 0) {
      toast.error('请选择至少一个教育机构');
      return;
    }

    if (!form.permissions.read && !form.permissions.download && !form.permissions.distribute) {
      toast.error('请至少选择一个权限');
      return;
    }

    try {
      setIsLoading(true);
      
      // Grant authorization to each selected tenant
      const promises = form.selected_tenants.map(tenant =>
        textbookAuthorizationApi.grantAuthorization({
          textbook_id: selectedAid.id,
          tenant_id: tenant.value,
          expires_at: form.expires_at || undefined,
          permissions: form.permissions,
          notes: form.notes || undefined,
        })
      );

      await Promise.all(promises);

      toast.success(`成功为 ${form.selected_tenants.length} 个教育机构授权`);
      handleClose();
      onAuthorizationSuccess?.();
    } catch (err) {
      console.error('Error granting authorization:', err);
      toast.error('授权失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTenantSelectionChange = (selectedOptions: Option[]) => {
    setForm(prev => ({
      ...prev,
      selected_tenants: selectedOptions
    }));
  };

  const updatePermission = (permission: keyof AuthorizationForm['permissions'], checked: boolean) => {
    setForm({
      ...form,
      permissions: { ...form.permissions, [permission]: checked }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>授权教辅给教育机构</DialogTitle>
          <DialogDescription>
            为教育机构授权访问教辅资源：{selectedAid?.title}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>选择教育机构 *</Label>
            <MultiSelect
              value={form.selected_tenants}
              onChange={handleTenantSelectionChange}
              options={tenantOptions}
              placeholder="请选择教育机构..."
              emptyIndicator="暂无可用的教育机构"
              loadingIndicator={
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  加载中...
                </div>
              }
              className="w-full"
              disabled={isLoadingTenants}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="expires-at">过期时间（可选）</Label>
            <Input
              id="expires-at"
              type="datetime-local"
              value={form.expires_at}
              onChange={(e) => setForm({ ...form, expires_at: e.target.value })}
            />
          </div>

          <div className="space-y-3">
            <Label>权限设置 *</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="permission-read"
                  checked={form.permissions.read}
                  onCheckedChange={(checked) => updatePermission('read', !!checked)}
                />
                <Label htmlFor="permission-read" className="flex items-center gap-2">
                  <Eye className="h-4 w-4 text-blue-500" />
                  可查看
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="permission-download"
                  checked={form.permissions.download}
                  onCheckedChange={(checked) => updatePermission('download', !!checked)}
                />
                <Label htmlFor="permission-download" className="flex items-center gap-2">
                  <Download className="h-4 w-4 text-green-500" />
                  可下载
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="permission-distribute"
                  checked={form.permissions.distribute}
                  onCheckedChange={(checked) => updatePermission('distribute', !!checked)}
                />
                <Label htmlFor="permission-distribute" className="flex items-center gap-2">
                  <Share2 className="h-4 w-4 text-purple-500" />
                  可分发
                </Label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">备注（可选）</Label>
            <Textarea
              id="notes"
              value={form.notes}
              onChange={(e) => setForm({ ...form, notes: e.target.value })}
              placeholder="授权备注信息"
              rows={3}
            />
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <Button 
            variant="outline" 
            onClick={handleClose}
            disabled={isLoading}
          >
            取消
          </Button>
          <Button 
            onClick={handleGrantAuthorization}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                授权中...
              </>
            ) : (
              '确认授权'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AuthorizationDialog;
