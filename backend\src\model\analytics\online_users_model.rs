use sqlx::{FromRow, Type};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct OnlineUser {
    pub id: Uuid,
    pub user_id: Uuid,
    pub tenant_id: Option<Uuid>,
    pub session_id: Uuid,
    
    // 状态信息
    pub status: String,
    pub last_activity_at: DateTime<Utc>,
    pub login_at: DateTime<Utc>,
    pub logout_at: Option<DateTime<Utc>>,
    
    // 会话信息
    pub current_page: Option<String>,
    pub current_module: Option<String>,
    pub session_duration_seconds: Option<i32>,
    
    // 环境信息
    pub ip_address: Option<std::net::IpAddr>,
    pub user_agent: Option<String>,
    pub device_type: Option<String>,
    pub browser: Option<String>,
    pub os: Option<String>,
    
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// 创建或更新在线用户状态的请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpsertOnlineUserRequest {
    pub user_id: Uuid,
    pub tenant_id: Option<Uuid>,
    pub session_id: Uuid,
    
    pub status: Option<String>,
    pub current_page: Option<String>,
    pub current_module: Option<String>,
    
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub device_type: Option<String>,
    pub browser: Option<String>,
    pub os: Option<String>,
}

// 在线用户查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OnlineUserQueryParams {
    pub tenant_id: Option<Uuid>,
    pub status: Option<String>,
    pub current_module: Option<String>,
    pub device_type: Option<String>,
    pub active_since: Option<DateTime<Utc>>,
    pub page: Option<i32>,
    pub page_size: Option<i32>,
}

// 在线用户统计数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OnlineUserStats {
    pub total_online: i64,
    pub by_status: HashMap<String, i64>,
    pub by_module: HashMap<String, i64>,
    pub by_device: HashMap<String, i64>,
    pub by_tenant: HashMap<String, i64>,
    pub peak_concurrent_today: i64,
    pub avg_session_duration_minutes: f64,
}

// 实时在线用户活动数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OnlineUserActivity {
    pub user_id: Uuid,
    pub username: Option<String>,
    pub display_name: Option<String>,
    pub tenant_name: Option<String>,
    pub status: String,
    pub current_page: Option<String>,
    pub current_module: Option<String>,
    pub last_activity_at: DateTime<Utc>,
    pub session_duration_minutes: i32,
    pub device_type: Option<String>,
    pub browser: Option<String>,
}

// 在线用户分布数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OnlineUserDistribution {
    pub total_count: i64,
    pub status_distribution: Vec<StatusCount>,
    pub module_distribution: Vec<ModuleCount>,
    pub device_distribution: Vec<DeviceCount>,
    pub tenant_distribution: Vec<TenantCount>,
    pub hourly_pattern: Vec<HourlyOnlineCount>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StatusCount {
    pub status: String,
    pub count: i64,
    pub percentage: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModuleCount {
    pub module: String,
    pub count: i64,
    pub percentage: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceCount {
    pub device_type: String,
    pub count: i64,
    pub percentage: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TenantCount {
    pub tenant_id: String,
    pub tenant_name: String,
    pub count: i64,
    pub percentage: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HourlyOnlineCount {
    pub hour: i32,
    pub count: i64,
    pub avg_session_duration_minutes: f64,
}

// 用户会话详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSessionDetail {
    pub session_id: Uuid,
    pub user_id: Uuid,
    pub username: Option<String>,
    pub tenant_id: Option<Uuid>,
    pub tenant_name: Option<String>,
    
    pub status: String,
    pub login_at: DateTime<Utc>,
    pub last_activity_at: DateTime<Utc>,
    pub session_duration_minutes: i32,
    
    pub current_page: Option<String>,
    pub current_module: Option<String>,
    
    pub ip_address: Option<std::net::IpAddr>,
    pub device_type: Option<String>,
    pub browser: Option<String>,
    pub os: Option<String>,
    
    pub pages_visited: Vec<String>,
    pub modules_used: Vec<String>,
    pub total_actions: i64,
}

// 在线用户实时指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OnlineUserRealTimeMetrics {
    pub current_online: i64,
    pub active_last_5min: i64,
    pub active_last_15min: i64,
    pub active_last_hour: i64,
    pub peak_today: i64,
    pub avg_concurrent_today: f64,
    pub new_sessions_today: i64,
    pub sessions_ended_today: i64,
} 