-- 用户行为分析系统迁移
-- Migration: 20250923_user_behavior_analytics.sql

-- 用户行为日志表（扩展现有审计日志功能）
CREATE TABLE IF NOT EXISTS public.user_behavior_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES public.tenants(id) ON DELETE CASCADE,
    session_id UUID NOT NULL,
    
    -- 行为信息
    action_type VARCHAR(50) NOT NULL, -- 'login', 'logout', 'page_view', 'click', 'download', 'upload', 'search', 'create', 'update', 'delete'
    module VARCHAR(50) NOT NULL,      -- 'exam', 'homework', 'grading', 'statistics', 'user_management', etc.
    page_path VARCHAR(255),           -- '/exam/create', '/homework/list', etc.
    element VARCHAR(100),             -- 点击的具体元素或操作对象
    
    -- 上下文信息
    resource_type VARCHAR(50),        -- 'exam', 'homework', 'student', 'class', etc.
    resource_id UUID,                 -- 具体资源ID
    resource_name VARCHAR(255),       -- 资源名称
    
    -- 请求信息
    request_method VARCHAR(10),       -- 'GET', 'POST', 'PUT', 'DELETE'
    request_url TEXT,                 -- 完整请求URL
    request_params JSONB,             -- 请求参数
    response_status INTEGER,          -- HTTP响应状态码
    response_time_ms INTEGER,         -- 响应时间（毫秒）
    
    -- 环境信息
    ip_address INET,
    user_agent TEXT,
    device_type VARCHAR(20),          -- 'desktop', 'mobile', 'tablet'
    browser VARCHAR(50),              -- 'chrome', 'firefox', 'safari', etc.
    os VARCHAR(50),                   -- 'windows', 'macos', 'linux', 'android', 'ios'
    screen_resolution VARCHAR(20),    -- '1920x1080', etc.
    
    -- 业务信息
    duration_ms INTEGER,              -- 操作持续时间
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,               -- 错误信息（如果有）
    additional_data JSONB,            -- 其他相关数据
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 在线用户状态表
CREATE TABLE IF NOT EXISTS public.online_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES public.tenants(id) ON DELETE CASCADE,
    session_id UUID UNIQUE NOT NULL,
    
    -- 状态信息
    status VARCHAR(20) NOT NULL DEFAULT 'online', -- 'online', 'idle', 'away', 'offline'
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    login_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    logout_at TIMESTAMP WITH TIME ZONE,
    
    -- 会话信息
    current_page VARCHAR(255),        -- 当前页面路径
    current_module VARCHAR(50),       -- 当前模块
    session_duration_seconds INTEGER DEFAULT 0,
    
    -- 环境信息
    ip_address INET,
    user_agent TEXT,
    device_type VARCHAR(20),
    browser VARCHAR(50),
    os VARCHAR(50),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_user_session UNIQUE (user_id, session_id)
);

-- 用户行为统计汇总表（用于快速查询）
CREATE TABLE IF NOT EXISTS public.user_behavior_summary (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES public.tenants(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    
    -- 活动统计
    login_count INTEGER DEFAULT 0,
    page_views INTEGER DEFAULT 0,
    total_actions INTEGER DEFAULT 0,
    session_count INTEGER DEFAULT 0,
    total_duration_seconds INTEGER DEFAULT 0,
    avg_session_duration_seconds INTEGER DEFAULT 0,
    
    -- 模块使用统计
    module_stats JSONB DEFAULT '{}',  -- {'exam': 10, 'homework': 5, 'grading': 3}
    
    -- 设备统计
    device_stats JSONB DEFAULT '{}', -- {'desktop': 8, 'mobile': 2}
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_user_date UNIQUE (user_id, tenant_id, date)
);

-- 系统访问统计表（租户级别）
CREATE TABLE IF NOT EXISTS public.system_usage_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES public.tenants(id) ON DELETE CASCADE,
    stat_date DATE NOT NULL,
    hour_of_day INTEGER CHECK (hour_of_day >= 0 AND hour_of_day <= 23),
    
    -- 用户统计
    unique_users INTEGER DEFAULT 0,
    total_sessions INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    returning_users INTEGER DEFAULT 0,
    
    -- 活动统计
    total_page_views INTEGER DEFAULT 0,
    total_actions INTEGER DEFAULT 0,
    avg_session_duration_seconds INTEGER DEFAULT 0,
    
    -- 热门功能
    top_modules JSONB DEFAULT '[]',   -- [{'module': 'exam', 'count': 100}, ...]
    top_pages JSONB DEFAULT '[]',     -- [{'page': '/exam/list', 'count': 50}, ...]
    
    -- 设备统计
    device_breakdown JSONB DEFAULT '{}', -- {'desktop': 70, 'mobile': 30}
    browser_breakdown JSONB DEFAULT '{}', -- {'chrome': 60, 'firefox': 40}
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_tenant_date_hour UNIQUE (tenant_id, stat_date, hour_of_day)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_user_tenant ON public.user_behavior_logs(user_id, tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_session ON public.user_behavior_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_action_type ON public.user_behavior_logs(action_type);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_module ON public.user_behavior_logs(module);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_created_at ON public.user_behavior_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_tenant_created ON public.user_behavior_logs(tenant_id, created_at);

CREATE INDEX IF NOT EXISTS idx_online_users_user_tenant ON public.online_users(user_id, tenant_id);
CREATE INDEX IF NOT EXISTS idx_online_users_session ON public.online_users(session_id);
CREATE INDEX IF NOT EXISTS idx_online_users_status ON public.online_users(status);
CREATE INDEX IF NOT EXISTS idx_online_users_last_activity ON public.online_users(last_activity_at);
CREATE INDEX IF NOT EXISTS idx_online_users_tenant_activity ON public.online_users(tenant_id, last_activity_at);

CREATE INDEX IF NOT EXISTS idx_user_behavior_summary_user_date ON public.user_behavior_summary(user_id, date);
CREATE INDEX IF NOT EXISTS idx_user_behavior_summary_tenant_date ON public.user_behavior_summary(tenant_id, date);

CREATE INDEX IF NOT EXISTS idx_system_usage_stats_tenant_date ON public.system_usage_stats(tenant_id, stat_date);
CREATE INDEX IF NOT EXISTS idx_system_usage_stats_date_hour ON public.system_usage_stats(stat_date, hour_of_day);

-- 添加表注释
COMMENT ON TABLE public.user_behavior_logs IS '用户行为日志表，记录用户在系统中的所有操作行为';
COMMENT ON TABLE public.online_users IS '在线用户状态表，实时跟踪用户在线状态和会话信息';  
COMMENT ON TABLE public.user_behavior_summary IS '用户行为统计汇总表，按日聚合用户活动数据';
COMMENT ON TABLE public.system_usage_stats IS '系统使用统计表，按租户和时间维度统计系统使用情况'; 