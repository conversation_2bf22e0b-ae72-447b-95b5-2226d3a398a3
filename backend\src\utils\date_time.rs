// utils/date_time.rs
use chrono::{DateTime, NaiveDateTime, Utc};
use serde::{self, Deserialize, Deserializer};

pub fn deserialize<'de, S>(deserializer: S) -> Result<DateTime<Utc>, S::Error>
where
    S: Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;
    let dt = NaiveDateTime::parse_from_str(&s, "%b %d, %Y, %I:%M:%S %p")
        .map_err(serde::de::Error::custom)?;
    Ok(DateTime::<Utc>::from_naive_utc_and_offset(dt, Utc))
}