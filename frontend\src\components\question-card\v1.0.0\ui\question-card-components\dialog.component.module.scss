@use '../global.module.scss' as base;

h2 {
  margin: 0;
  padding: 0;
}

.dialog-overlay {
  background-color: rgba(0, 0, 0, 0.3);
  position: fixed;
  inset: 0;
  animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
}

.dialog-trigger {
  @extend .unset;
}

.dialog-content {
  background-color: base.$background-color;
  border-radius: 6px;
  position: fixed;
  top: 50px;
  left: 0;
  right: 0;
  width: 800px;
  margin: auto;
  height: fit-content;
  padding: 10px;
  animation: contentShow 300ms cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 100;

  .dialog-title {
    margin-bottom: 10px;
  }

  .dialog-description {
    @extend .unset;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: auto;
    max-height: 70vh;
  }
  .dialog-footer {
    padding: 0;
    margin-top: 10px;
    background-color: base.$background-color;
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  &:focus {
    outline: none;
  }

  .dialog-close {
    @extend .unset;
    @extend .hover-default;
    position: absolute;
    top: 10px;
    right: 10px;
    width: 32px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
  }
}
