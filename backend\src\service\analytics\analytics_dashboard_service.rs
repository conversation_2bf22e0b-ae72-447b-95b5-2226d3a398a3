use anyhow::Result;
use sqlx::PgPool;
use uuid::Uuid;

use crate::model::analytics::analytics_dto::*;
use crate::repository::analytics::analytics_repository::AnalyticsRepository;
use crate::middleware::auth_middleware::AuthContext;

/// 综合分析看板服务
pub struct AnalyticsDashboardService {
    analytics_repo: AnalyticsRepository,
}

impl AnalyticsDashboardService {
    pub fn new(pool: PgPool) -> Self {
        Self {
            analytics_repo: AnalyticsRepository::new(pool),
        }
    }

    /// 获取分析总览数据
    pub async fn get_overview(&self, tenant_id: Option<Uuid>, context: &AuthContext) -> Result<AnalyticsOverview> {
        let authorized_tenant_id = self.get_authorized_tenant_id(tenant_id, context).await?;
        self.analytics_repo.get_analytics_overview(authorized_tenant_id).await
    }

    /// 获取实时指标
    pub async fn get_real_time_metrics(&self, tenant_id: Option<Uuid>, context: &AuthContext) -> Result<RealTimeMetrics> {
        let authorized_tenant_id = self.get_authorized_tenant_id(tenant_id, context).await?;
        self.analytics_repo.get_real_time_metrics(authorized_tenant_id).await
    }

    /// 获取用户有权限查看的租户ID
    async fn get_authorized_tenant_id(
        &self,
        requested_tenant_id: Option<Uuid>,
        context: &AuthContext,
    ) -> Result<Option<Uuid>> {
        if self.is_super_admin(context).await? {
            Ok(requested_tenant_id)
        } else {
            Ok(context.tenant_links.first().map(|link| link.tenant_id))
        }
    }

    /// 检查是否为超级管理员
    async fn is_super_admin(&self, _context: &AuthContext) -> Result<bool> {
        Ok(false) // TODO: 实现实际权限检查
    }
} 